// Import cubing.js for WCA scramble generation and twisty.js for visualization
// Using the recommended /v0/ namespace for backward compatibility
import { randomScrambleForEvent } from "https://cdn.cubing.net/v0/js/cubing/scramble";
import { Alg } from "https://cdn.cubing.net/v0/js/cubing/alg";
import { TwistyPlayer } from "https://cdn.cubing.net/v0/js/cubing/twisty";

// Import audio manager for better sound handling
import {
  play8SecSound,
  play12SecSound,
  playVoiceSound,
  unlockAudio,
} from "./audio-manager.js";

// Import MBLD manager for multi-blind functionality
import {
  initMBLD,
  showCubeCountModal,
  generateMBLDScrambles,
  showMBLDScrambles,
  showMBLDVisualizations,
  showMBLDResultsModal,
  isMBLDInProgress,
  resetMBLD,
  getMBLDCubeCount,
} from "./mbld-manager.js";

// Import FMC manager for fewest moves challenge functionality
import {
  initFMC,
  generateFMCScramble,
  setupFMCInterface,
  startFMCAttempt,
  isFMCInProgress,
  resetFMC,
  restartFMCWithNewScramble,
  updateFMCKeyboardLabelState,
  nukeAllFMCElements,
} from "./fmc-manager.js";

// Import internationalization module
// Language handling is now done by language-manager.js

// No sound system import needed - using direct button clicks

document.addEventListener("DOMContentLoaded", function () {
  // DOM elements
  const timerElement = document.getElementById("timer");
  let scrambleElement = document.getElementById("scramble"); // Changed to let instead of const
  const newScrambleButton = document.getElementById("new-scramble");
  const timesList = document.getElementById("times-list");
  let bestTimeElement = document.getElementById("best-time");
  let meanTimeElement = document.getElementById("mean-time");
  let avg5Element = document.getElementById("avg5");
  let avg12Element = document.getElementById("avg12");
  let avg100Element = document.getElementById("avg100");
  let solveCountElement = document.getElementById("solve-count");
  const clearTimesButton = document.getElementById("clear-times-btn");
  const deleteSessionButton = document.getElementById("delete-session-btn");
  const timerStateElement = document.getElementById("timer-state");
  const spaceHeldTimeElement = document.getElementById("space-held-time");
  const currentEventElement = document.getElementById("current-event");
  const currentEventText = document.getElementById("current-event-text");
  const currentEventIcon = document.getElementById("current-event-icon");
  const scrambleSourceElement = document.getElementById("scramble-source");
  const visualizationContainer = document.getElementById(
    "visualization-container"
  );
  const showVisualizationCheckbox =
    document.getElementById("show-visualization");
  const showStatsCheckbox = document.getElementById("show-stats");
  const showFMCKeyboardCheckbox = document.getElementById("show-fmc-keyboard");
  const showFMCKeyboardLabel = document.getElementById(
    "show-fmc-keyboard-label"
  );
  const useInspectionCheckbox = document.getElementById("use-inspection");
  const inspectionSoundSelector = document.getElementById(
    "inspection-sound-selector"
  );
  const stackmatResetInspectionCheckbox = document.getElementById(
    "stackmat-reset-inspection"
  );
  const timerModeSelector = document.getElementById("timer-mode-selector");
  const microphoneSelector = document.getElementById("microphone-selector");
  const manualInputContainer = document.getElementById(
    "manual-input-container"
  );
  const manualTimeInput = document.getElementById("manual-time-input");
  const manualInspection = document.getElementById("manual-inspection");
  const darkModeCheckbox = document.getElementById("dark-mode");
  const scrambleFontSizeSlider = document.getElementById("scramble-font-size");
  const scrambleFontSizeValue = document.getElementById(
    "scramble-font-size-value"
  );

  const eventSelectorBtn = document.getElementById("event-selector-btn");
  const eventDropdown = document.getElementById("event-dropdown");
  const eventOptions = document.querySelectorAll(".event-option");

  // New UI elements
  const settingsButton = document.getElementById("settings-btn");
  const settingsModal = document.getElementById("settings-modal");
  const settingsClose = document.getElementById("settings-close");
  const settingsSave = document.getElementById("settings-save");
  const timesToggle = document.getElementById("times-toggle");
  const timesPanel = document.getElementById("times-panel");
  const timesPanelClose = document.getElementById("times-panel-close");

  // Stackmat elements
  const stackmatStatus = document.getElementById("stackmat-status");
  const stackmatConnectionText = document.getElementById(
    "stackmat-connection-text"
  );

  // Custom dropdown for events
  eventSelectorBtn.addEventListener("click", function () {
    eventDropdown.classList.toggle("show");
  });

  // Close dropdown when clicking outside
  document.addEventListener("click", function (event) {
    if (
      !eventSelectorBtn.contains(event.target) &&
      !eventDropdown.contains(event.target)
    ) {
      eventDropdown.classList.remove("show");
    }
  });

  // Event selection
  eventOptions.forEach(function (option) {
    option.addEventListener("click", function () {
      // Check if this is the "New Session" option
      const action = this.getAttribute("data-action");
      if (action === "new-session") {
        // Close dropdown
        eventDropdown.classList.remove("show");

        // Show the new session modal
        showNewSessionModal();
        return; // Exit early
      }

      const eventId = this.getAttribute("data-event");
      // Get the text from the span element, not the entire element
      const textSpan = this.querySelector("span[data-i18n]");
      const eventText = textSpan
        ? textSpan.textContent.trim()
        : this.textContent.trim();

      // Special handling for problematic event transitions
      // Direct transition with thorough cleanup

      // Case 1: FMC to MBLD
      if (eventId === "333mbf" && currentEvent === "333fm") {
        // SPECIAL HANDLING: Direct transition from FMC to MBLD with thorough cleanup

        // Perform thorough cleanup first
        thoroughCleanup();

        // Update button text and icon for MBLD
        currentEventText.textContent = eventText;

        // Set the appropriate icon class
        if (eventId.startsWith("unofficial-")) {
          // For unofficial events, use the correct class format
          currentEventIcon.className = `cubing-icon ${eventId}`;
        } else {
          // For WCA events, use the event- prefix
          currentEventIcon.className = `cubing-icon event-${eventId}`;
        }

        // Update current event to MBLD and reset session ID
        currentEvent = eventId;
        currentEventElement.textContent = eventId;
        currentSessionId = null; // Reset session ID for default events

        // Close dropdown
        eventDropdown.classList.remove("show");

        // Update FMC keyboard label state
        updateFMCKeyboardLabelState();

        // Process the MBLD event change
        processEventChange(eventId, eventText);

        // Save the current event state
        saveCurrentEventAndSession();

        return; // Exit early
      }

      // Case 2: MBLD to FMC
      if (eventId === "333fm" && currentEvent === "333mbf") {
        // SPECIAL HANDLING: Direct transition from MBLD to FMC with thorough cleanup

        // Perform thorough cleanup first
        thoroughCleanup();

        // Update button text and icon for FMC
        currentEventText.textContent = eventText;

        // Set the appropriate icon class
        if (eventId.startsWith("unofficial-")) {
          // For unofficial events, use the correct class format
          currentEventIcon.className = `cubing-icon ${eventId}`;
        } else {
          // For WCA events, use the event- prefix
          currentEventIcon.className = `cubing-icon event-${eventId}`;
        }

        // Update current event to FMC and reset session ID
        currentEvent = eventId;
        currentEventElement.textContent = eventId;
        currentSessionId = null; // Reset session ID for default events

        // Close dropdown
        eventDropdown.classList.remove("show");

        // Update FMC keyboard label state
        updateFMCKeyboardLabelState();

        // Process the FMC event change
        processEventChange(eventId, eventText);

        // Save the current event state
        saveCurrentEventAndSession();

        // Special handling for FMC - ensure timer is visible after event change
        // Immediate fix
        window.ensureTimerVisible();

        // Delayed fix to ensure it happens after all async operations
        setTimeout(() => {
          window.ensureTimerVisible();
        }, 500);

        // Another delayed fix with longer timeout as a fallback
        setTimeout(() => {
          window.ensureTimerVisible();
        }, 1000);

        return; // Exit early
      }

      // Normal event switching
      // Update button text and icon
      currentEventText.textContent = eventText;

      // Set the appropriate icon class
      // For non-WCA puzzles, use the unofficial- prefix for icons only
      const nonWcaPuzzleIcons = {
        fto: "unofficial-fto",
        master_tetraminx: "unofficial-mtetram",
        kilominx: "unofficial-kilominx",
        redi_cube: "unofficial-redi",
        baby_fto: "unofficial-baby_fto",
      };

      if (nonWcaPuzzleIcons[eventId] !== undefined) {
        // For non-WCA puzzles with icons, use the unofficial- prefix
        if (nonWcaPuzzleIcons[eventId]) {
          currentEventIcon.className = `cubing-icon ${nonWcaPuzzleIcons[eventId]}`;
        } else {
          // For puzzles without icons, just use a generic icon or none
          currentEventIcon.className = ""; // No icon
        }
      } else {
        // For WCA events, use the event- prefix
        currentEventIcon.className = `cubing-icon event-${eventId}`;
      }

      // Update current event and reset session ID for default events
      currentEvent = eventId;
      currentEventElement.textContent = eventId;
      currentSessionId = null; // Reset session ID for default events

      // Close dropdown
      eventDropdown.classList.remove("show");

      // Update FMC keyboard label state
      updateFMCKeyboardLabelState();

      // Process the event change
      processEventChange(eventId, eventText);

      // Save the current event state
      saveCurrentEventAndSession();

      // Special handling for FMC - ensure timer is visible after event change
      if (eventId === "333fm") {
        // Immediate fix
        window.ensureTimerVisible();

        // Delayed fix to ensure it happens after all async operations
        setTimeout(() => {
          window.ensureTimerVisible();
        }, 500);

        // Another delayed fix with longer timeout as a fallback
        setTimeout(() => {
          window.ensureTimerVisible();
        }, 1000);
      }
    });
  });

  // Function to thoroughly clean up both FMC and MBLD elements
  function thoroughCleanup() {
    // 1. Reset timer display
    if (timerElement) {
      timerElement.textContent = formatTime(0); // Use formatTime to ensure consistent "0.000" format
      timerElement.style.display = "block";
      timerElement.style.fontSize = "";
      timerElement.style.color = "";
      timerElement.classList.remove("fmc-start-prompt");
    }

    // 2. Reset scramble
    if (scrambleElement) {
      scrambleElement.style.visibility = "visible";

      // Remove MBLD-specific properties
      scrambleElement.classList.remove("mbld-clickable");
      scrambleElement.removeAttribute("title");

      // Reset the scramble text
      const scrambleText = document.getElementById("scramble-text");
      if (scrambleText) {
        scrambleText.textContent = "";
        scrambleText.classList.remove("hidden");
        scrambleText.style.display = "inline";
      }

      // Reset the scramble loader
      const scrambleLoader = document.getElementById("scramble-loader");
      if (scrambleLoader) {
        scrambleLoader.style.display = "none";
      }
    }

    // 3. Remove FMC elements
    const fmcElements = [
      "fmc-solution-container",
      "fmc-keyboard-container",
      "fmc-result-modal",
      "fmc-visualization-modal",
      "fmc-timer-display",
      "fmc-move-count",
      "fmc-solution-input",
      "fmc-validation-message",
      "fmc-submit-button",
      "fmc-keyboard",
      "fmc-visualization-container",
    ];

    fmcElements.forEach((id) => {
      const element = document.getElementById(id);
      if (element) {
        element.remove();
      }
    });

    // 4. Hide MBLD modals (don't remove them)
    const mbldModals = [
      "mbld-cube-count-modal",
      "mbld-scrambles-modal",
      "mbld-visualizations-modal",
      "mbld-results-modal",
    ];

    mbldModals.forEach((id) => {
      const element = document.getElementById(id);
      if (element) {
        element.classList.remove("show");

        // Clear the content of containers inside modals
        if (id === "mbld-scrambles-modal") {
          const container = element.querySelector("#mbld-scrambles-container");
          if (container) {
            container.innerHTML = "";
          }
        } else if (id === "mbld-visualizations-modal") {
          const container = element.querySelector(
            "#mbld-visualizations-container"
          );
          if (container) {
            container.innerHTML = "";
          }
        }
      }
    });

    // 5. Remove other modals (not MBLD)
    const modals = document.querySelectorAll(
      '.modal:not([id^="mbld-"]):not([id^="fmc-"])'
    );
    modals.forEach((modal) => {
      modal.remove();
    });

    // 6. Reset visualization container
    const visualizationContainer = document.getElementById(
      "visualization-container"
    );
    if (visualizationContainer) {
      visualizationContainer.style.display = "block";
      visualizationContainer.classList.remove("hidden");

      // Reset any twisty player
      const twistyPlayer =
        visualizationContainer.querySelector("twisty-player");
      if (twistyPlayer) {
        twistyPlayer.style.display = "block";
      }
    }

    // 7. Reset stats container
    const statsContainer = document.querySelector(".stats-container");
    if (statsContainer) {
      statsContainer.style.display = "block";
      statsContainer.classList.remove("hidden");

      // Restore the original stats HTML structure
      statsContainer.innerHTML = `
        <div class="stats">
          <div class="stat-item">
            <div class="stat-label" data-i18n="stats.solves">Solves:</div>
            <div class="stat-value" id="solve-count">0</div>
          </div>
          <div class="stat-item">
            <div class="stat-label" data-i18n="stats.best">Best:</div>
            <div class="stat-value" id="best-time">-</div>
          </div>
          <div class="stat-item">
            <div class="stat-label" data-i18n="stats.mean">Mean:</div>
            <div class="stat-value" id="mean-time">-</div>
          </div>
          <div class="stat-item">
            <div class="stat-label" data-i18n="stats.avg5">ao5:</div>
            <div class="stat-value" id="avg5">-</div>
          </div>
          <div class="stat-item">
            <div class="stat-label" data-i18n="stats.avg12">ao12:</div>
            <div class="stat-value" id="avg12">-</div>
          </div>
          <div class="stat-item">
            <div class="stat-label" data-i18n="stats.avg100">ao100:</div>
            <div class="stat-value" id="avg100">-</div>
          </div>
        </div>
      `;

      // Remove any FMC or MBLD specific classes
      statsContainer.classList.remove("fmc-stats", "mbld-stats");
    }

    // 8. Re-enable checkboxes
    const checkboxes = [
      { id: "show-stats", defaultChecked: true },
      { id: "show-visualization", defaultChecked: true },
      { id: "use-inspection", defaultChecked: true },
      { id: "show-fmc-keyboard", defaultChecked: true },
    ];

    checkboxes.forEach((checkbox) => {
      const element = document.getElementById(checkbox.id);
      if (element) {
        element.disabled = false;
        if (
          element.parentElement &&
          element.parentElement.classList.contains("disabled")
        ) {
          element.parentElement.classList.remove("disabled");
        }

        // Restore saved setting or use default
        const savedSetting = localStorage.getItem(
          `scTimer-${checkbox.id.replace("show-", "").replace("use-", "")}`
        );
        if (savedSetting === "false") {
          element.checked = false;
        } else {
          element.checked = checkbox.defaultChecked;
        }
      }
    });

    // 9. Force a refresh of the visualization
    if (typeof updateVisualization === "function") {
      setTimeout(updateVisualization, 100);
    }
  }

  // Global function to ensure the timer is visible
  window.ensureTimerVisible = function (message = null) {
    const timerElement = document.getElementById("timer");
    if (timerElement) {
      timerElement.style.display = "block";
      timerElement.style.visibility = "visible";
      timerElement.style.opacity = "1";

      // Set a message if provided
      if (message) {
        timerElement.textContent = message;
      }
    }
  };

  // Function to process event change
  function processEventChange(eventId, eventText) {
    // Step 1: Clean up the UI completely
    cleanupUI();

    // Step 2: Set up the UI based on the event type
    setupUIForEvent(eventId);

    // Special handling for FMC - ensure timer is visible immediately
    if (eventId === "333fm") {
      window.ensureTimerVisible();
    }

    // Step 3: Load times for the current event
    loadTimes();

    // Step 4: Generate appropriate scramble for the event
    generateScrambleForEvent(eventId);

    // Special handling for FMC - ensure timer is visible after scramble generation
    if (eventId === "333fm") {
      setTimeout(() => {
        window.ensureTimerVisible();
      }, 100);
    }

    // Step 5: Force update statistics for the new event
    // Always call updateStats() which handles all event types properly
    updateStats();

    // Update the stat labels with the current translations
    updateStatLabels();

    // Make sure stats container is visible for regular events
    if (eventId !== "333fm" && eventId !== "333mbf") {
      const statsContainer = document.querySelector(".stats-container");
      if (statsContainer) {
        statsContainer.style.display = "block";
        statsContainer.classList.remove("hidden");
      }

      // Enable stats checkbox for regular events
      const showStatsCheckbox = document.getElementById("show-stats");
      if (showStatsCheckbox) {
        showStatsCheckbox.disabled = false;
        if (showStatsCheckbox.parentElement) {
          showStatsCheckbox.parentElement.classList.remove("disabled");
        }
      }
    }

    // Step 6: Show/hide edit session button based on whether this is a custom session
    const editSessionButton = document.getElementById("edit-session-btn");
    if (editSessionButton) {
      // Check if this is a custom session
      const isCustomSession = customSessions.some(
        (session) =>
          session.puzzleType === eventId && session.name === eventText
      );

      if (isCustomSession) {
        editSessionButton.classList.remove("hidden");

        // Find the session and set the currentSessionId
        const session = customSessions.find(
          (session) =>
            session.puzzleType === eventId && session.name === eventText
        );

        if (session) {
          currentSessionId = session.id;
        }
      } else {
        editSessionButton.classList.add("hidden");
        currentSessionId = null; // Reset session ID for default events
      }
    }
  }

  // Function to completely clean up the UI
  function cleanupUI() {
    // 1. Reset timer display
    if (timerElement) {
      timerElement.textContent = formatTime(0); // Use formatTime to ensure consistent "0.000" format
      timerElement.style.display = "block";
      timerElement.style.fontSize = "";
      timerElement.style.color = "";
      timerElement.classList.remove("fmc-start-prompt");
    }

    // 2. Reset scramble element
    if (scrambleElement) {
      // Create a completely new scramble element to remove all event listeners
      const newScrambleElement = document.createElement("div");
      newScrambleElement.id = "scramble";
      newScrambleElement.className = "scramble";

      // Copy the content structure
      newScrambleElement.innerHTML = `
        <div class="scramble-loader" id="scramble-loader" style="display: none;"></div>
        <span id="scramble-text" class="hidden"></span>
      `;

      // Replace the old element
      if (scrambleElement.parentNode) {
        scrambleElement.parentNode.replaceChild(
          newScrambleElement,
          scrambleElement
        );
        scrambleElement = newScrambleElement;
      }

      // Make scramble visible by default
      scrambleElement.style.visibility = "visible";

      // Re-setup scramble gestures after element replacement
      window.setupScrambleGestures();
    }

    // 3. Remove FMC elements
    // Use nukeAllFMCElements if available, otherwise use fallback
    if (typeof nukeAllFMCElements === "function") {
      nukeAllFMCElements();
    } else {
      // Fallback cleanup for FMC elements
      const fmcElements = [
        "fmc-solution-container",
        "fmc-keyboard-container",
        "fmc-result-modal",
        "fmc-visualization-modal",
        "fmc-timer-display",
        "fmc-move-count",
        "fmc-solution-input",
        "fmc-validation-message",
        "fmc-submit-button",
        "fmc-keyboard",
        "fmc-visualization-container",
      ];

      fmcElements.forEach((id) => {
        const element = document.getElementById(id);
        if (element) {
          element.remove();
        }
      });

      // Remove any elements with FMC in the ID (catch-all)
      const allFMCElements = document.querySelectorAll('[id*="fmc"]');
      allFMCElements.forEach((element) => {
        element.remove();
      });
    }

    // 4. Hide MBLD modals (don't remove them)
    const mbldModals = [
      "mbld-cube-count-modal",
      "mbld-scrambles-modal",
      "mbld-visualizations-modal",
      "mbld-results-modal",
    ];

    mbldModals.forEach((id) => {
      const element = document.getElementById(id);
      if (element) {
        element.classList.remove("show");

        // Clear the content of containers inside modals
        if (id === "mbld-scrambles-modal") {
          const container = element.querySelector("#mbld-scrambles-container");
          if (container) {
            container.innerHTML = "";
          }
        } else if (id === "mbld-visualizations-modal") {
          const container = element.querySelector(
            "#mbld-visualizations-container"
          );
          if (container) {
            container.innerHTML = "";
          }
        }
      }
    });

    // 5. Reset MBLD state
    resetMBLD();

    // 6. Reset FMC state
    resetFMC();

    // 7. Reset visualization container
    const visualizationContainer = document.getElementById(
      "visualization-container"
    );
    if (visualizationContainer) {
      visualizationContainer.style.display = "block";

      // Reset any twisty player
      const twistyPlayer =
        visualizationContainer.querySelector("twisty-player");
      if (twistyPlayer) {
        twistyPlayer.remove();
      }
    }

    // 8. Reset stats container
    const statsContainer = document.querySelector(".stats-container");
    if (statsContainer) {
      statsContainer.style.display = "block";

      // Restore the original stats HTML structure
      statsContainer.innerHTML = `
        <div class="stats">
          <div class="stat-item">
            <div class="stat-label" data-i18n="stats.solves">Solves:</div>
            <div class="stat-value" id="solve-count">0</div>
          </div>
          <div class="stat-item">
            <div class="stat-label" data-i18n="stats.best">Best:</div>
            <div class="stat-value" id="best-time">-</div>
          </div>
          <div class="stat-item">
            <div class="stat-label" data-i18n="stats.mean">Mean:</div>
            <div class="stat-value" id="mean-time">-</div>
          </div>
          <div class="stat-item">
            <div class="stat-label" data-i18n="stats.avg5">ao5:</div>
            <div class="stat-value" id="avg5">-</div>
          </div>
          <div class="stat-item">
            <div class="stat-label" data-i18n="stats.avg12">ao12:</div>
            <div class="stat-value" id="avg12">-</div>
          </div>
          <div class="stat-item">
            <div class="stat-label" data-i18n="stats.avg100">ao100:</div>
            <div class="stat-value" id="avg100">-</div>
          </div>
        </div>
      `;

      // Remove any FMC or MBLD specific classes
      statsContainer.classList.remove("fmc-stats", "mbld-stats");

      // Re-get the DOM elements after rebuilding the structure
      solveCountElement = document.getElementById("solve-count");
      bestTimeElement = document.getElementById("best-time");
      meanTimeElement = document.getElementById("mean-time");
      avg5Element = document.getElementById("avg5");
      avg12Element = document.getElementById("avg12");
      avg100Element = document.getElementById("avg100");
    }

    // 9. Reset checkboxes to default state
    const checkboxes = [
      { id: "show-stats", element: showStatsCheckbox },
      { id: "show-visualization", element: showVisualizationCheckbox },
      { id: "use-inspection", element: useInspectionCheckbox },
      {
        id: "stackmat-reset-inspection",
        element: stackmatResetInspectionCheckbox,
      },
      { id: "show-fmc-keyboard", element: showFMCKeyboardCheckbox },
    ];

    checkboxes.forEach(({ id, element }) => {
      if (element) {
        element.disabled = false;
        if (element.parentElement) {
          element.parentElement.classList.remove("disabled");
        }
      }
    });

    // Reset inspection sound dropdown to default state
    if (inspectionSoundSelector) {
      inspectionSoundSelector.disabled = false;
      inspectionSoundSelector.value = "beep";
      if (inspectionSoundSelector.parentElement) {
        inspectionSoundSelector.parentElement.classList.remove("disabled");
      }
    }

    // 10. Reset current scramble
    currentScramble = "";

    // UI cleanup complete
  }

  // Function to update decimal places selector state based on event
  function updateDecimalPlacesSelectorState(isFMCEvent) {
    const decimalPlacesSelector = document.getElementById(
      "decimal-places-selector"
    );
    if (decimalPlacesSelector) {
      if (isFMCEvent) {
        // Disable decimal places selector for FMC
        decimalPlacesSelector.disabled = true;
        decimalPlacesSelector.style.opacity = "0.5";
        decimalPlacesSelector.style.cursor = "not-allowed";

        // Add disabled class to parent label for styling
        const parentLabel = decimalPlacesSelector.closest(".settings-option");
        if (parentLabel) {
          parentLabel.classList.add("disabled");
        }
      } else {
        // Enable decimal places selector for other events
        decimalPlacesSelector.disabled = false;
        decimalPlacesSelector.style.opacity = "1";
        decimalPlacesSelector.style.cursor = "pointer";

        // Remove disabled class from parent label
        const parentLabel = decimalPlacesSelector.closest(".settings-option");
        if (parentLabel) {
          parentLabel.classList.remove("disabled");
        }
      }
    }
  }

  // Function to set up the UI based on the event type
  function setupUIForEvent(eventId) {
    // Add the event class to the scramble element
    if (scrambleElement) {
      scrambleElement.classList.add(`event-${eventId}`);
    }

    // Check event type
    const isBlindEvent = ["333bf", "444bf", "555bf", "333mbf"].includes(
      eventId
    );
    const isFMCEvent = eventId === "333fm";
    const isMBLDEvent = eventId === "333mbf";

    // Update decimal places selector state based on event
    updateDecimalPlacesSelectorState(isFMCEvent);

    // 1. Set up inspection checkbox
    if (useInspectionCheckbox) {
      if (isBlindEvent || isFMCEvent) {
        // Disable inspection for blind events and FMC
        useInspectionCheckbox.disabled = true;
        useInspectionCheckbox.checked = false;
        if (useInspectionCheckbox.parentElement) {
          useInspectionCheckbox.parentElement.classList.add("disabled");
        }

        // Also disable inspection sounds
        if (inspectionSoundSelector) {
          inspectionSoundSelector.disabled = true;
          inspectionSoundSelector.value = "none";
          if (inspectionSoundSelector.parentElement) {
            inspectionSoundSelector.parentElement.classList.add("disabled");
          }
        }
      } else {
        // Enable inspection for regular events
        useInspectionCheckbox.disabled = false;
        useInspectionCheckbox.checked = true;
        if (useInspectionCheckbox.parentElement) {
          useInspectionCheckbox.parentElement.classList.remove("disabled");
        }

        // Enable inspection sounds
        if (inspectionSoundSelector) {
          inspectionSoundSelector.disabled = false;
          // Restore saved setting
          const inspectionSound =
            localStorage.getItem("scTimer-inspectionSound") || "beep";
          inspectionSoundSelector.value = inspectionSound;
          if (inspectionSoundSelector.parentElement) {
            inspectionSoundSelector.parentElement.classList.remove("disabled");
          }
        }
      }
    }

    // 2. Set up visualization checkbox and container
    if (showVisualizationCheckbox) {
      if (isFMCEvent) {
        // Disable visualization for FMC
        showVisualizationCheckbox.disabled = true;
        showVisualizationCheckbox.checked = false;
        if (showVisualizationCheckbox.parentElement) {
          showVisualizationCheckbox.parentElement.classList.add("disabled");
        }

        // Hide visualization container
        const visualizationContainer = document.querySelector(".visualization");
        if (visualizationContainer) {
          visualizationContainer.classList.add("hidden");
        }
      } else {
        // Enable visualization for other events
        showVisualizationCheckbox.disabled = false;
        if (showVisualizationCheckbox.parentElement) {
          showVisualizationCheckbox.parentElement.classList.remove("disabled");
        }

        // Restore saved setting
        const showVisualization = localStorage.getItem(
          "scTimer-showVisualization"
        );
        showVisualizationCheckbox.checked = showVisualization !== "false";

        // Show/hide visualization container based on checkbox
        const visualizationContainer = document.querySelector(".visualization");
        if (visualizationContainer) {
          visualizationContainer.classList.toggle(
            "hidden",
            !showVisualizationCheckbox.checked
          );
        }
      }
    }

    // 3. Set up stats checkbox and container
    if (showStatsCheckbox) {
      if (isFMCEvent && isFMCInProgress()) {
        // Disable stats for FMC during attempt
        showStatsCheckbox.disabled = true;
        showStatsCheckbox.checked = false;
        if (showStatsCheckbox.parentElement) {
          showStatsCheckbox.parentElement.classList.add("disabled");
        }

        // Hide stats container
        const statsContainer = document.querySelector(".stats-container");
        if (statsContainer) {
          statsContainer.classList.add("hidden");
        }
      } else {
        // Enable stats for other events and FMC before attempt
        showStatsCheckbox.disabled = false;
        if (showStatsCheckbox.parentElement) {
          showStatsCheckbox.parentElement.classList.remove("disabled");
        }

        // Restore saved setting
        const showStats = localStorage.getItem("scTimer-showStats");
        showStatsCheckbox.checked = showStats !== "false";

        // Show/hide stats container based on checkbox
        const statsContainer = document.querySelector(".stats-container");
        if (statsContainer) {
          statsContainer.classList.toggle("hidden", !showStatsCheckbox.checked);
        }
      }
    }

    // 4. Set up FMC keyboard checkbox
    if (showFMCKeyboardCheckbox) {
      if (isFMCEvent) {
        // Enable FMC keyboard checkbox for FMC
        showFMCKeyboardCheckbox.disabled = false;
        if (showFMCKeyboardCheckbox.parentElement) {
          showFMCKeyboardCheckbox.parentElement.classList.remove("disabled");
        }

        // Restore saved setting
        const showFMCKeyboard = localStorage.getItem("scTimer-showFMCKeyboard");
        showFMCKeyboardCheckbox.checked = showFMCKeyboard !== "false";
      } else {
        // Disable FMC keyboard checkbox for other events
        showFMCKeyboardCheckbox.disabled = true;
        showFMCKeyboardCheckbox.checked = false;
        if (showFMCKeyboardCheckbox.parentElement) {
          showFMCKeyboardCheckbox.parentElement.classList.add("disabled");
        }
      }
    }

    // 5. Set up timer mode selector
    if (timerModeSelector) {
      // Get all options
      const typingOption = Array.from(timerModeSelector.options).find(
        (option) => option.value === "typing"
      );
      const stackmatOption = Array.from(timerModeSelector.options).find(
        (option) => option.value === "stackmat"
      );
      const bluetoothOption = Array.from(timerModeSelector.options).find(
        (option) => option.value === "bluetooth"
      );

      if (isFMCEvent || isMBLDEvent) {
        // Disable typing, stackmat, and bluetooth modes for FMC and MBLD events
        if (typingOption) {
          typingOption.disabled = true;
        }
        if (stackmatOption) {
          stackmatOption.disabled = true;
        }
        if (bluetoothOption) {
          bluetoothOption.disabled = true;
        }

        // If currently in any disabled mode, switch to timer mode
        if (
          ["typing", "stackmat", "bluetooth"].includes(timerModeSelector.value)
        ) {
          // Stop Stackmat if it was active
          if (timerModeSelector.value === "stackmat") {
            handleStackmatModeChange(false);
          }

          timerModeSelector.value = "timer";
          localStorage.setItem("scTimer-timerMode", "timer");
          toggleManualInputTimer(false);
        }
      } else {
        // Enable all modes for other events (except bluetooth which is globally disabled)
        if (typingOption) {
          typingOption.disabled = false;
        }
        if (stackmatOption) {
          stackmatOption.disabled = false;
        }
        // Bluetooth remains disabled globally as it's not implemented yet

        // Restore saved setting
        const timerMode = localStorage.getItem("scTimer-timerMode") || "timer";
        timerModeSelector.value = timerMode;

        // Apply the timer mode
        if (timerMode === "typing") {
          toggleManualInputTimer(true);
        } else if (timerMode === "stackmat") {
          handleStackmatModeChange(true);
        } else {
          toggleManualInputTimer(false);
        }
      }
    }

    // 5. Special setup for MBLD
    if (isMBLDEvent) {
      // Initialize MBLD
      initMBLD();

      // Show cube count modal
      showCubeCountModal();
    }

    // 6. Special setup for FMC
    if (isFMCEvent) {
      // Make sure the timer is visible immediately
      const timerElement = document.getElementById("timer");
      if (timerElement) {
        timerElement.style.display = "block";
        timerElement.style.visibility = "visible";
        timerElement.style.opacity = "1";

        // Don't set text here - let FMC manager handle the loading state
      }

      // Initialize FMC by importing the module
      import("./fmc-manager.js")
        .then(({ initFMC, setupFMCInterface }) => {
          // Initialize FMC
          initFMC().then(() => {
            // Setup FMC interface - this will handle the loading state and timer display
            setupFMCInterface();
          });
        })
        .catch((error) => {
          // Error importing FMC module

          // If there's an error, show error message
          if (timerElement) {
            timerElement.textContent =
              "Error loading FMC. Please refresh the page.";
            timerElement.style.display = "block";
            timerElement.style.visibility = "visible";
            timerElement.style.opacity = "1";
            timerElement.style.color = "#e74c3c";
          }
        });

      // Hide scramble until attempt starts
      if (scrambleElement) {
        scrambleElement.style.visibility = "hidden";
      }
    }

    // UI setup complete for event
  }

  // Function to generate appropriate scramble for the event
  function generateScrambleForEvent(eventId) {
    // Reset the scramble generation flag
    window.isGeneratingScramble = false;

    if (eventId === "333fm") {
      // Show loader while generating
      const scrambleLoader = document.getElementById("scramble-loader");
      const scrambleText = document.getElementById("scramble-text");

      if (scrambleLoader) {
        scrambleLoader.style.display = "inline-block";
      }

      if (scrambleText) {
        scrambleText.classList.add("hidden");
      }

      // For FMC, the scramble generation is handled by setupFMCInterface
      // Just hide the loader since FMC manager will handle everything
      if (scrambleLoader) {
        scrambleLoader.style.display = "none";
      }

      if (scrambleText) {
        scrambleText.classList.add("hidden");
      }
    } else if (eventId === "333mbf") {
      // MBLD scrambles are generated when the cube count modal is submitted
      // No need to generate them here
    } else if (eventId === "kilominx") {
      // Special handling for kilominx - generate with longer timeout

      // Show loader while generating
      const scrambleLoader = document.getElementById("scramble-loader");
      const scrambleText = document.getElementById("scramble-text");

      if (scrambleLoader) {
        scrambleLoader.style.display = "inline-block";
      }

      if (scrambleText) {
        scrambleText.classList.add("hidden");
      }

      // Use a custom function with longer timeout for kilominx
      generateKilominxScramble()
        .then((scramble) => {
          // Ensure the scramble text is updated
          if (scrambleText) {
            scrambleText.textContent = currentScramble;
            scrambleText.classList.remove("hidden");
          }

          // Hide loader
          if (scrambleLoader) {
            scrambleLoader.style.display = "none";
          }

          // Force update visualization with the new scramble
          if (typeof updateVisualization === "function") {
            setTimeout(updateVisualization, 100);
          }
        })
        .catch((error) => {
          // Error generating kilominx scramble

          // Hide loader
          if (scrambleLoader) {
            scrambleLoader.style.display = "none";
          }

          // Show error message instead of fallback
          if (scrambleText) {
            scrambleText.textContent =
              "Error: Unable to generate WCA kilominx scramble. Please refresh the page.";
            scrambleText.style.color = "#e74c3c";
            scrambleText.classList.remove("hidden");
          }

          // Reset the flag
          window.isGeneratingScramble = false;
        });
    } else {
      // Generate regular scramble for other events
      generateScramble()
        .then((scramble) => {
          // Ensure the scramble text is updated
          const scrambleText = document.getElementById("scramble-text");
          if (scrambleText) {
            scrambleText.textContent = currentScramble;
            scrambleText.classList.remove("hidden");
          }

          // Force update visualization with the new scramble
          if (typeof updateVisualization === "function") {
            setTimeout(updateVisualization, 100);
          }
        })
        .catch((error) => {
          // Error generating scramble
          window.isGeneratingScramble = false;
        });
    }
  }
  // Twisty player instance
  let twistyPlayer = null;

  // Debug info toggle removed

  // Toggle visualization
  showVisualizationCheckbox.addEventListener("change", function () {
    visualizationContainer.classList.toggle("hidden", !this.checked);
    if (this.checked) {
      // For MBLD, make sure we update the visualization properly
      if (currentEvent === "333mbf") {
        updateVisualization();
      } else if (currentScramble) {
        updateVisualization();
      }
    }
  });

  // Toggle stats
  const statsContainer = document.querySelector(".stats-container");
  showStatsCheckbox.addEventListener("change", function () {
    statsContainer.classList.toggle("hidden", !this.checked);
  });

  // Timer states
  const TIMER_STATE = {
    IDLE: "IDLE",
    INSPECTION: "INSPECTION",
    HOLDING: "HOLDING",
    READY: "READY",
    RUNNING: "RUNNING",
  };

  // Inspection constants
  const INSPECTION_TIME = 15; // 15 seconds per WCA regulations
  const INSPECTION_PENALTY_PLUS2 = 15; // +2 penalty at 15 seconds
  const INSPECTION_PENALTY_DNF = 17; // DNF penalty at 17 seconds

  // Timer variables
  let timerState = TIMER_STATE.IDLE;
  let startTime = 0;
  let elapsedTime = 0;
  let timerInterval = null;
  let spaceDownTime = 0;
  let spaceHeldInterval = null;
  let inspectionStartTime = 0;
  let inspectionInterval = null;
  let inspectionTimeLeft = INSPECTION_TIME;
  let inspectionPenalty = null; // null, '+2', or 'DNF'
  const HOLD_THRESHOLD = 500; // ms to hold before ready state

  // Current event and scramble
  let currentEvent = "333";
  let currentScramble = "";
  let currentSessionId = null; // Track the current session ID for custom sessions

  // Initialize scramble edited flag
  window.isScrambleEdited = false;

  // Stackmat manager
  let stackmatManager = null;
  let isStackmatMode = false;

  // We always set inspection to checked for non-blind events

  // Times storage
  let timesMap = {};

  // Custom sessions storage
  let customSessions = [];

  // Load saved times from localStorage
  loadTimes();

  // Note: Initial scramble generation moved to after event restoration

  // New Scramble button
  if (newScrambleButton) {
    newScrambleButton.addEventListener("click", function () {
      // Check if we're already generating a scramble
      if (window.isGeneratingScramble) {
        return;
      }

      // Check if this is an FMC attempt in progress
      if (currentEvent === "333fm") {
        // For FMC, generate a new scramble
        if (isFMCInProgress()) {
          // If an attempt is in progress, restart with a new scramble
          restartFMCWithNewScramble();
        } else {
          // If no attempt is in progress, generate a new scramble using the same method as initial selection
          // Show loader while generating
          const scrambleLoader = document.getElementById("scramble-loader");
          const scrambleText = document.getElementById("scramble-text");

          if (scrambleLoader) {
            scrambleLoader.style.display = "inline-block";
          }

          if (scrambleText) {
            scrambleText.classList.add("hidden");
          }

          // For FMC new scramble, use the restart function
          import("./fmc-manager.js")
            .then(({ restartFMCWithNewScramble }) => {
              // Hide loader
              if (scrambleLoader) {
                scrambleLoader.style.display = "none";
              }

              // Use the restart function which handles everything properly
              restartFMCWithNewScramble();
            })
            .catch((error) => {
              // Error loading FMC manager for new scramble

              // Hide loader
              if (scrambleLoader) {
                scrambleLoader.style.display = "none";
              }

              // Show error message
              if (scrambleText) {
                scrambleText.textContent = "Error loading FMC module";
                scrambleText.classList.remove("hidden");
              }
            });
        }
      } else if (currentEvent === "333mbf" && isMBLDInProgress()) {
        // For MBLD, show cube count modal to restart
        showCubeCountModal();
      } else {
        // For regular events, just generate a new scramble
        generateScramble();
      }
    });
  }

  // Language system is now initialized by language-manager.js

  // Format time in milliseconds to display format (HH:MM:SS.ms)
  function formatTime(timeInMs) {
    // Get decimal places setting
    const decimalPlaces = parseInt(
      localStorage.getItem("scTimer-decimalPlaces") || "3"
    );

    // Handle special cases
    if (timeInMs === 0) {
      if (decimalPlaces === 0) return "0";
      return "0." + "0".repeat(decimalPlaces);
    }
    if (timeInMs === Infinity || isNaN(timeInMs)) return "DNF";
    if (!isFinite(timeInMs)) {
      if (decimalPlaces === 0) return "0";
      return "0." + "0".repeat(decimalPlaces);
    }

    // Ensure timeInMs is a valid number
    timeInMs = Math.max(0, Number(timeInMs));

    // Convert to hours, minutes, seconds, and milliseconds
    const totalSeconds = timeInMs / 1000;

    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = Math.floor(totalSeconds % 60);

    // Format decimal part based on setting
    let decimalPart = "";
    if (decimalPlaces > 0) {
      const fractionalSeconds = totalSeconds - Math.floor(totalSeconds);
      const multiplier = Math.pow(10, decimalPlaces);
      const decimalValue = Math.floor(fractionalSeconds * multiplier);
      decimalPart = "." + decimalValue.toString().padStart(decimalPlaces, "0");
    }

    // Build the time string based on the magnitude
    if (hours > 0) {
      // Format: HH:MM:SS.ms or HH:MM:SS
      return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds
        .toString()
        .padStart(2, "0")}${decimalPart}`;
    } else if (minutes > 0) {
      // Format: MM:SS.ms or MM:SS
      return `${minutes}:${seconds.toString().padStart(2, "0")}${decimalPart}`;
    } else {
      // Format: SS.ms or SS
      return `${seconds}${decimalPart}`;
    }
  }

  // Update timer display
  function updateDisplay(timeInMs) {
    // If inspection is running (even during HOLDING or READY states)
    if (inspectionInterval !== null) {
      // Display inspection time left
      if (inspectionTimeLeft <= 0) {
        // Show +2 penalty
        timerElement.textContent = "+2";
      } else {
        timerElement.textContent = Math.ceil(inspectionTimeLeft);
      }

      // Add ready indicator if in READY state
      if (timerState === TIMER_STATE.READY) {
        timerElement.classList.add("ready");
      }
    } else if (timerState === TIMER_STATE.INSPECTION) {
      // Fallback for inspection state without interval
      if (inspectionTimeLeft <= 0) {
        timerElement.textContent = "+2";
      } else {
        timerElement.textContent = Math.ceil(inspectionTimeLeft);
      }
    } else {
      // Display solve time
      timerElement.textContent = formatTime(timeInMs);
    }
  }

  // Update timer state display
  function updateTimerState(state) {
    // Store previous state for reference
    const previousState = timerState;

    timerState = state;
    timerStateElement.textContent = state;

    // Update timer appearance based on state
    timerElement.classList.remove("ready", "running");

    // Only remove inspection class if we're not in HOLDING or READY during inspection
    if (state !== TIMER_STATE.HOLDING && state !== TIMER_STATE.READY) {
      timerElement.classList.remove("inspection");
    }

    if (state === TIMER_STATE.READY) {
      timerElement.classList.add("ready");
      // If we're in ready state during inspection, keep the inspection class
      if (inspectionInterval) {
        timerElement.classList.add("inspection");
      }
    } else if (state === TIMER_STATE.RUNNING) {
      // When starting the actual solve, stop the inspection timer
      if (inspectionInterval) {
        stopInspection();
      }
      timerElement.classList.add("running");
    } else if (state === TIMER_STATE.INSPECTION) {
      timerElement.classList.add("inspection");
    } else if (
      state === TIMER_STATE.HOLDING &&
      previousState === TIMER_STATE.INSPECTION
    ) {
      // Keep the inspection class if we're holding during inspection
      timerElement.classList.add("inspection");
    }
  }

  // Variables to track sound timeouts
  let sound8SecTimeout = null;
  let sound12SecTimeout = null;

  // Variable to track times panel state before inspection
  let timesPanelWasOpenBeforeInspection = false;

  // Start inspection timer
  function startInspection() {
    // Unlock audio on iOS (in case it's needed)
    unlockAudio();

    // Save current times panel state and close it if open
    timesPanelWasOpenBeforeInspection = timesPanel.classList.contains("show");
    if (timesPanelWasOpenBeforeInspection) {
      // Close the panel
      closeTimesPanel();
    }

    // Change times toggle to X (cancel) icon
    const icon = timesToggle.querySelector("i");
    if (icon) {
      icon.classList.remove("fa-history");
      icon.classList.add("fa-times");
    }

    // Play inspection sounds if enabled
    if (inspectionSoundSelector && inspectionSoundSelector.value !== "none") {
      const soundType = inspectionSoundSelector.value;

      // Schedule 8-second warning (7 seconds into inspection)
      sound8SecTimeout = setTimeout(function () {
        if (soundType === "voice") {
          playVoiceSound("8sec");
        } else if (soundType === "beep") {
          play8SecSound();
        }
      }, 7000);

      // Schedule 12-second warning (12 seconds into inspection)
      sound12SecTimeout = setTimeout(function () {
        if (soundType === "voice") {
          playVoiceSound("12sec");
        } else if (soundType === "beep") {
          play12SecSound();
        }
      }, 12000);
    }

    // Reset inspection variables
    inspectionStartTime = Date.now();
    inspectionTimeLeft = INSPECTION_TIME;
    inspectionPenalty = null;

    // Check if we're in manual input mode
    const isManualInputMode =
      timerModeSelector && timerModeSelector.value === "typing";

    // Add inspection-active class to hide UI elements
    document.body.classList.add("inspection-active");

    // Update display to show inspection time
    updateDisplay(0);
    updateTimerState(TIMER_STATE.INSPECTION);

    // Start inspection interval
    inspectionInterval = setInterval(function () {
      const elapsedInspectionTime = (Date.now() - inspectionStartTime) / 1000;
      inspectionTimeLeft = INSPECTION_TIME - elapsedInspectionTime;

      // Only apply penalties if not in manual input mode
      if (!isManualInputMode) {
        if (inspectionTimeLeft <= 0 && inspectionTimeLeft > -2) {
          // +2 penalty zone (15-17 seconds)
          inspectionPenalty = "+2";
        } else if (inspectionTimeLeft <= -2) {
          // DNF penalty zone (>17 seconds)
          inspectionPenalty = "DNF";
          stopInspection();
          resetTimer();
          generateScramble();

          // Save a DNF
          const dnfEntry = {
            time: Infinity,
            date: new Date().toISOString(),
            scramble: currentScramble,
            isScrambleEdited: window.isScrambleEdited || false,
            penalty: "DNF",
            result: "DNF",
          };

          if (!timesMap[currentEvent]) {
            timesMap[currentEvent] = [];
          }

          timesMap[currentEvent].unshift(dnfEntry);
          updateTimesList();
          updateStats();
          saveTimes();
        }
      } else {
        // In manual input mode, show inspection time with proper indicators
        // Update the manual inspection display
        if (manualInspection) {
          if (inspectionTimeLeft > 0) {
            manualInspection.textContent = Math.ceil(inspectionTimeLeft);
            manualInspection.classList.remove("warning");
          } else if (inspectionTimeLeft > -2) {
            // Show +2 when in the +2 penalty zone (15-17 seconds)
            manualInspection.textContent = "+2";
            manualInspection.classList.add("warning");
          } else {
            // Show DNF when in the DNF penalty zone (>17 seconds)
            manualInspection.textContent = "DNF";
            manualInspection.classList.add("warning");
          }
        }
      }

      // Update the display
      updateDisplay(0);
    }, 100);
  }

  // Stop inspection timer
  function stopInspection() {
    clearInterval(inspectionInterval);
    inspectionInterval = null;

    // Cancel scheduled sound button clicks
    if (sound8SecTimeout) {
      clearTimeout(sound8SecTimeout);
      sound8SecTimeout = null;
    }

    if (sound12SecTimeout) {
      clearTimeout(sound12SecTimeout);
      sound12SecTimeout = null;
    }

    // Remove inspection-active class to show UI elements again
    // (unless we're starting the timer, in which case timing-active will be added)
    if (timerState !== TIMER_STATE.READY) {
      document.body.classList.remove("inspection-active");
    }
  }

  // Start the timer
  function startTimer() {
    startTime = Date.now();
    elapsedTime = 0;

    // Remove inspection-active class if it's set
    document.body.classList.remove("inspection-active");

    // Add timing-active class to hide UI elements
    document.body.classList.add("timing-active");

    timerInterval = setInterval(function () {
      elapsedTime = Date.now() - startTime;
      updateDisplay(elapsedTime);
    }, 10); // Update every 10ms for smooth display

    updateTimerState(TIMER_STATE.RUNNING);
  }

  // Stop the timer
  function stopTimer() {
    clearInterval(timerInterval);

    // Remove timing-active class to show UI elements again
    document.body.classList.remove("timing-active");

    // Check if this is an MBLD attempt
    if (currentEvent === "333mbf" && isMBLDInProgress()) {
      // Show the MBLD results modal instead of saving time directly
      showMBLDResultsModal();

      // Listen for the MBLD result saved event
      document.addEventListener(
        "mbldResultSaved",
        function handleMBLDResult(e) {
          // Remove the event listener to avoid duplicates
          document.removeEventListener("mbldResultSaved", handleMBLDResult);

          // Save the MBLD result to the solve list
          if (e.detail) {
            saveTime(0, e.detail);
          }

          // Generate new MBLD scrambles with the same cube count
          generateMBLDScrambles(getMBLDCubeCount());
        },
        { once: true }
      );
    }
    // Check if this is an FMC attempt
    else if (currentEvent === "333fm" && isFMCInProgress()) {
      // FMC is handled by the FMC manager
      // The timer is not used for FMC, but we'll stop it just in case
      resetTimer();
    } else {
      // Regular event - save the time
      saveTime(elapsedTime);

      // Generate new scramble
      generateScramble();
    }

    updateTimerState(TIMER_STATE.IDLE);

    // Restore times panel state if it was open before inspection
    if (timesPanelWasOpenBeforeInspection) {
      // Reopen the panel
      timesPanel.classList.add("show");

      // Add class to body for large screen layout
      if (window.innerWidth >= 1024) {
        document.body.classList.add("panel-open");
        const container = document.querySelector(".container");
        container.style.marginLeft = "350px";
        container.style.width = "calc(100% - 350px)";
        container.style.transform = "scale(0.9)";
        container.style.transformOrigin = "center center";
      }

      // Update toggle button to show close icon
      timesToggle.classList.add("active");
      const icon = timesToggle.querySelector("i");
      if (icon) {
        icon.classList.remove("fa-history");
        icon.classList.add("fa-times");
      }

      // Update toggle position
      updateTimesTogglePosition();
    } else {
      // Reset times toggle to history icon if panel was closed
      const icon = timesToggle.querySelector("i");
      if (icon) {
        icon.classList.remove("fa-times");
        icon.classList.add("fa-history");
      }
      timesToggle.classList.remove("active");
    }

    // Reset the state tracking variable
    timesPanelWasOpenBeforeInspection = false;

    return elapsedTime;
  }

  // Reset the timer
  function resetTimer() {
    clearInterval(timerInterval);
    elapsedTime = 0;
    updateDisplay(0);
    updateTimerState(TIMER_STATE.IDLE);

    // Make sure timing-active and inspection-active classes are removed
    document.body.classList.remove("timing-active");
    document.body.classList.remove("inspection-active");
  }

  // Save a time
  window.saveTime = function saveTime(
    time,
    mbldResult = null,
    fmcResult = null
  ) {
    // Determine the storage key based on whether we're in a custom session or not
    const storageKey = currentSessionId
      ? `session_${currentSessionId}`
      : currentEvent;

    // Initialize the array if it doesn't exist
    if (!timesMap[storageKey]) {
      timesMap[storageKey] = [];
    }

    // If this is an MBLD result
    if (mbldResult) {
      // Create an MBLD-specific time entry
      const timeEntry = {
        time: mbldResult.isDNF ? Infinity : mbldResult.time,
        date: new Date().toISOString(),
        scramble:
          mbldResult.scrambles && mbldResult.scrambles.length > 0
            ? mbldResult.scrambles[0]
            : "",
        // Store all MBLD-specific data directly in the time entry
        solved: mbldResult.solved,
        total: mbldResult.total,
        score: mbldResult.score,
        scrambles: mbldResult.scrambles,
        result: mbldResult.result,
        isMBLD: true,
        penalty: mbldResult.isDNF ? "DNF" : null,
        originalTime: mbldResult.isDNF ? mbldResult.time : null,
        // Only include comment if explicitly provided
        comment: mbldResult.comment || null,
      };

      timesMap[storageKey].unshift(timeEntry);
    }
    // If this is an FMC result
    else if (fmcResult) {
      // Create an FMC-specific time entry
      const timeEntry = {
        time: fmcResult.isDNF ? Infinity : fmcResult.time,
        date: new Date().toISOString(),
        scramble: fmcResult.scramble,
        solution: fmcResult.solution,
        moveCount: fmcResult.moveCount,
        result: fmcResult.result,
        isFMC: true,
        penalty: fmcResult.isDNF ? "DNF" : null,
        dnfReason: fmcResult.dnfReason,
        originalTime: fmcResult.isDNF ? fmcResult.time : null,
      };

      timesMap[storageKey].unshift(timeEntry);

      // Generate a new FMC scramble for the next attempt
      generateFMCScramble().then((scramble) => {
        currentScramble = scramble;
      });
    } else {
      // Regular solve time entry
      const timeEntry = {
        time: time,
        date: new Date().toISOString(),
        scramble: currentScramble,
        isScrambleEdited: window.isScrambleEdited || false,
        penalty: null,
      };

      // Check if we're in manual input mode
      const isManualInputMode =
        timerModeSelector && timerModeSelector.value === "typing";

      // Only apply penalties if not in manual input mode
      if (!isManualInputMode) {
        timeEntry.penalty = inspectionPenalty;

        if (inspectionPenalty === "+2") {
          timeEntry.time += 2000; // Add 2 seconds (2000ms)
          timeEntry.result = formatTime(timeEntry.time) + " (+2)";
        } else if (inspectionPenalty === "DNF") {
          // Store the original time before setting to Infinity
          // Make sure we store a valid time
          if (isFinite(timeEntry.time)) {
            timeEntry.originalTime = timeEntry.time;
          }
          timeEntry.time = Infinity;
          timeEntry.result = "DNF";
          timeEntry.penalty = "DNF";
        } else {
          timeEntry.result = formatTime(timeEntry.time);
        }
      } else {
        // In manual input mode, never apply penalties
        timeEntry.result = formatTime(timeEntry.time);
      }

      timesMap[storageKey].unshift(timeEntry);
    }

    updateTimesList();
    updateStats();
    saveTimes();

    // Reset inspection penalty
    inspectionPenalty = null;

    // Clear the manual inspection display
    if (manualInspection) {
      manualInspection.textContent = "";
    }

    // Stop inspection if it's running
    stopInspection();

    // Don't restart inspection automatically - wait for user input
  };

  // Solve details modal elements
  const solveDetailsModal = document.getElementById("solve-details-modal");
  const solveDetailsClose = document.getElementById("solve-details-close");
  const solveDetailsSave = document.getElementById("solve-details-save");
  const solveDetailsShare = document.getElementById("solve-details-share");
  const solveDetailsTimeValue = document.getElementById(
    "solve-details-time-value"
  );
  const solveDetailsDateValue = document.getElementById(
    "solve-details-date-value"
  );
  const solveDetailsScramble = document.getElementById(
    "solve-details-scramble"
  );
  const solveDetailsComment = document.getElementById("solve-details-comment");
  const penaltyRadios = document.querySelectorAll('input[name="penalty"]');

  // Current selected solve index for details modal
  let currentSolveIndex = -1;

  // Update the times list
  function updateTimesList() {
    timesList.innerHTML = "";

    // Determine the storage key based on whether we're in a custom session or not
    const storageKey = currentSessionId
      ? `session_${currentSessionId}`
      : currentEvent;

    const times = timesMap[storageKey] || [];

    times.forEach(function (entry, index) {
      const li = document.createElement("li");
      li.className = "time-entry";

      // Create time info container
      const timeInfo = document.createElement("div");
      timeInfo.className = "time-info";

      // Create time value element
      const timeValue = document.createElement("div");
      timeValue.className = "time-value";

      // Add time text
      const timeText = document.createElement("span");
      if (entry.isMBLD) {
        // Format MBLD result: score (time)
        timeText.className = "mbld-result";

        // Create score element
        const scoreElement = document.createElement("span");
        scoreElement.className = "mbld-score";
        scoreElement.textContent = `${entry.score} (${entry.solved}/${entry.total})`;

        // Create time element
        const timeElement = document.createElement("span");
        timeElement.className = "mbld-time";
        timeElement.textContent = formatTime(
          entry.time === Infinity ? entry.originalTime : entry.time
        );

        // Add both elements
        timeText.appendChild(scoreElement);
        timeText.appendChild(timeElement);
      } else if (entry.isFMC) {
        // Format FMC result: moveCount (time)
        timeText.className = "fmc-result";

        // Get translations
        const translations = window.i18nModule?.translations || {};
        const fmcTranslations = translations.fmc || {};

        // Create move count element
        const moveCountElement = document.createElement("span");
        moveCountElement.className = "fmc-move-count";
        moveCountElement.textContent = entry.isDNF
          ? "DNF"
          : `${entry.moveCount} ${fmcTranslations.moves || "moves"}`;

        // Create time element
        const timeElement = document.createElement("span");
        timeElement.className = "fmc-time";

        // Format elapsed time (MM:SS) - handle undefined/NaN values
        const elapsedTime = entry.elapsedTime || 0;
        const minutes = Math.floor(elapsedTime / 60000);
        const seconds = Math.floor((elapsedTime % 60000) / 1000);
        const timeString = `${minutes}:${seconds.toString().padStart(2, "0")}`;

        timeElement.textContent = timeString;

        // Add both elements
        timeText.appendChild(moveCountElement);
        timeText.appendChild(timeElement);
      } else {
        // Always use formatTime for consistent decimal places, then add penalty suffix
        let timeDisplay = formatTime(entry.time);

        // Add penalty suffix if applicable
        if (entry.penalty === "+2") {
          timeDisplay += " (+2)";
        } else if (entry.penalty === "DNF") {
          timeDisplay = "DNF";
        }

        timeText.textContent = timeDisplay;
      }
      timeValue.appendChild(timeText);

      // Add comment indicator after the time if there's a comment
      if (entry.comment) {
        const commentIndicator = document.createElement("span");
        commentIndicator.className = "comment-indicator";
        commentIndicator.innerHTML = '<i class="fas fa-comment"></i>';
        timeValue.appendChild(commentIndicator);
      }

      // Create time date element
      const timeDate = document.createElement("div");
      timeDate.className = "time-date";
      const date = new Date(entry.date);
      timeDate.textContent = `${formatDate(date)} ${date.toLocaleTimeString()}`;

      // Add time info elements
      timeInfo.appendChild(timeValue);
      timeInfo.appendChild(timeDate);

      // Create delete button
      const deleteBtn = document.createElement("button");
      deleteBtn.innerHTML = '<i class="fas fa-times"></i>';
      deleteBtn.className = "delete-time";
      deleteBtn.type = "button";
      deleteBtn.title = "Delete time";
      deleteBtn.addEventListener("click", function (e) {
        e.stopPropagation(); // Prevent opening details when clicking delete

        // Get translations
        const translations = window.i18nModule?.translations || {};
        const timesTranslations = translations.times || {};
        const modalTranslations = translations.modals || {};
        const buttonTranslations = translations.buttons || {};

        import("./modal-manager.js").then(({ showConfirm }) => {
          showConfirm(
            timesTranslations.confirmDelete ||
              "Are you sure you want to delete this time?",
            function () {
              // Determine the storage key based on whether we're in a custom session or not
              const storageKey = currentSessionId
                ? `session_${currentSessionId}`
                : currentEvent;

              timesMap[storageKey].splice(index, 1);
              updateTimesList();
              updateStats();
              saveTimes();

              // Update timer display to show new most recent time (or 0.000 if no times left)
              if (timerState === TIMER_STATE.IDLE) {
                const newMostRecentTime = getPreviousTime();
                timerElement.textContent = formatTime(newMostRecentTime);
                updateDisplay(newMostRecentTime);
              }

              // On mobile, close the times panel after deleting
              if (
                window.innerWidth <= 768 &&
                timesPanel.classList.contains("show")
              ) {
                // Small delay to allow the user to see the updated list briefly
                setTimeout(() => {
                  closeTimesPanel();
                }, 300);
              }
            },
            null,
            modalTranslations.confirm || "Confirm"
          );
        });
      });

      // Add elements to list item
      li.appendChild(timeInfo);
      li.appendChild(deleteBtn);

      // Make the whole li clickable to show solve details
      li.addEventListener("click", function (e) {
        if (e.target !== deleteBtn && !deleteBtn.contains(e.target)) {
          showSolveDetails(index);
        }
      });

      timesList.appendChild(li);
    });
  }

  // Helper function to update times toggle button position based on language direction
  function updateTimesTogglePosition() {
    // Force remove any inline styles that might be interfering
    timesToggle.style.removeProperty("left");
    timesToggle.style.removeProperty("right");

    // Then apply the correct position based on language direction
    // Check if we're in RTL mode
    if (document.documentElement.getAttribute("dir") === "rtl") {
      timesToggle.style.left = "15px";
      timesToggle.style.right = "auto";
    } else {
      timesToggle.style.right = "15px";
      timesToggle.style.left = "auto";
    }

    // Common styles
    timesToggle.style.bottom = "15px";
    timesToggle.style.position = "fixed";
    timesToggle.style.zIndex = "2500";
  }

  // Function to update statistics labels with current translations
  function updateStatLabels() {
    try {
      // Get the translations object from the window object (set by language-manager.js)
      const { translations } = window.i18nModule || {};

      if (!translations || !translations.stats) {
        return;
      }

      // Update the statistics section title
      const statsTitle = document.querySelector(".stats-title");
      if (statsTitle) {
        statsTitle.textContent = translations.stats.title;
      }

      // Update all stat labels - use a more direct approach
      const statLabels = document.querySelectorAll(".stat-label");
      if (statLabels.length >= 6) {
        // Get all the stat items
        const statItems = document.querySelectorAll(".stat-item");

        // Update each stat item based on its position
        statItems.forEach((item, index) => {
          const label = item.querySelector(".stat-label");
          if (label) {
            // Update based on index position rather than content
            switch (index) {
              case 0: // Solves
                label.textContent = translations.stats.solves + ":";
                break;
              case 1: // Best
                label.textContent = translations.stats.best + ":";
                break;
              case 2: // Mean
                label.textContent = translations.stats.mean + ":";
                break;
              case 3: // ao5
                label.textContent = translations.stats.avg5 + ":";
                break;
              case 4: // ao12
                label.textContent = translations.stats.avg12 + ":";
                break;
              case 5: // ao100
                label.textContent = translations.stats.avg100 + ":";
                break;
            }
          }
        });
      }
    } catch (error) {
      // Silent error handling
    }
  }

  // Expose functions to the window object so they can be called from language-manager.js
  window.updateTimesTogglePosition = updateTimesTogglePosition;
  window.updateStatistics = function () {
    // First update the actual statistics values
    updateStats();
    // Then update the labels with the current translations
    updateStatLabels();

    // For FMC and MBLD, make sure we're using the correct stats display
    if (currentEvent === "333fm") {
      if (typeof setupFMCStatsDisplay === "function") {
        setupFMCStatsDisplay();
      }
    } else if (currentEvent === "333mbf") {
      if (typeof updateMBLDStatsDisplay === "function") {
        updateMBLDStatsDisplay();
      }
    }
  };

  // Set up a MutationObserver to watch for changes to the document's dir attribute
  const dirObserver = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === "attributes" && mutation.attributeName === "dir") {
        // Update the times toggle button position when the dir attribute changes
        updateTimesTogglePosition();
      }
    });
  });

  // Start observing the document element for dir attribute changes
  dirObserver.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ["dir"],
  });

  // Helper function to close the times panel
  function closeTimesPanel() {
    timesPanel.classList.remove("show");
    document.body.classList.remove("panel-open");
    const container = document.querySelector(".container");
    container.style.marginLeft = "0";
    container.style.width = "100%";
    container.style.transform = "scale(1)";

    // Update toggle button
    timesToggle.classList.remove("active");
    const icon = timesToggle.querySelector("i");
    icon.classList.remove("fa-times");
    icon.classList.add("fa-history");

    // Ensure toggle button is in the correct position
    updateTimesTogglePosition();

    // Save the times list state
    saveTimesListState();
  }

  // Show solve details modal
  function showSolveDetails(index) {
    // Determine the storage key based on whether we're in a custom session or not
    const storageKey = currentSessionId
      ? `session_${currentSessionId}`
      : currentEvent;

    const solve = timesMap[storageKey][index];
    currentSolveIndex = index;

    // On mobile, close the times panel first
    if (window.innerWidth <= 768 && timesPanel.classList.contains("show")) {
      closeTimesPanel();

      // Small delay to ensure panel is closed before opening modal
      setTimeout(() => {
        showSolveDetailsContent(solve);
      }, 100);
    } else {
      showSolveDetailsContent(solve);
    }
  }

  // Helper function to populate and show solve details
  function showSolveDetailsContent(solve) {
    // Set time and date
    if (solve.isMBLD) {
      // Format MBLD result
      solveDetailsTimeValue.textContent = `${solve.score} (${solve.solved}/${
        solve.total
      }) in ${formatTime(
        solve.time === Infinity ? solve.originalTime : solve.time
      )}`;
    } else if (solve.isFMC) {
      // Get translations
      const translations = window.i18nModule?.translations || {};
      const fmcTranslations = translations.fmc || {};

      // Format FMC result
      if (solve.isDNF || solve.penalty === "DNF") {
        solveDetailsTimeValue.textContent = `DNF (${
          solve.dnfReason || "Unknown reason"
        })`;
      } else {
        // Format elapsed time (MM:SS) - handle undefined/NaN values
        const elapsedTime = solve.elapsedTime || 0;
        const minutes = Math.floor(elapsedTime / 60000);
        const seconds = Math.floor((elapsedTime % 60000) / 1000);
        const timeString = `${minutes}:${seconds.toString().padStart(2, "0")}`;

        solveDetailsTimeValue.textContent = `${solve.moveCount} ${
          fmcTranslations.moves || "moves"
        } in ${timeString}`;

        // Add validation status
        const validationStatus = document.createElement("div");
        validationStatus.className = "fmc-result-validation";

        if (solve.isValid) {
          validationStatus.textContent =
            fmcTranslations.solutionAccepted || "Solution accepted";
          validationStatus.classList.add("fmc-result-valid");

          // Add a note about manual verification
          const verificationNote = document.createElement("div");
          verificationNote.className = "fmc-verification-note";
          verificationNote.textContent =
            fmcTranslations.clickToViewTwizzle ||
            "Click the link below to view the solution in Twizzle";
          validationStatus.appendChild(verificationNote);
        } else {
          validationStatus.textContent = `DNF: ${
            solve.dnfReason || "Invalid solution"
          }`;
          validationStatus.classList.add("fmc-result-dnf");
        }

        // Add validation link if available
        if (solve.validationUrl) {
          const validationLink = document.createElement("a");
          validationLink.href = solve.validationUrl;
          validationLink.target = "_blank";
          validationLink.className = "fmc-validation-link";
          validationLink.textContent =
            fmcTranslations.viewOnTwizzle || "View on Twizzle";

          // Add link to validation status
          validationStatus.appendChild(document.createElement("br"));
          validationStatus.appendChild(validationLink);
        }

        // Add validation status to time value
        solveDetailsTimeValue.appendChild(document.createElement("br"));
        solveDetailsTimeValue.appendChild(validationStatus);
      }
    } else {
      // Always use formatTime for consistent decimal places, then add penalty suffix
      let timeDisplay = formatTime(solve.time);

      // Add penalty suffix if applicable
      if (solve.penalty === "+2") {
        timeDisplay += " (+2)";
      } else if (solve.penalty === "DNF") {
        timeDisplay = "DNF";
      }

      solveDetailsTimeValue.textContent = timeDisplay;
    }

    const date = new Date(solve.date);
    solveDetailsDateValue.textContent = `${formatDate(
      date
    )} ${date.toLocaleTimeString()}`;

    // Set scramble
    if (solve.isMBLD && solve.scrambles && solve.scrambles.length > 0) {
      // For MBLD, show all scrambles
      solveDetailsScramble.innerHTML = "";

      // Create a container for all scrambles
      const scramblesContainer = document.createElement("div");
      scramblesContainer.className = "mbld-scrambles-container";

      // Get scrambles
      const scrambles = solve.scrambles;

      // Add each scramble
      scrambles.forEach((scramble, index) => {
        const scrambleItem = document.createElement("div");
        scrambleItem.className = "mbld-solve-details-scramble-item";

        const scrambleHeader = document.createElement("div");
        scrambleHeader.className = "mbld-solve-details-scramble-header";
        scrambleHeader.textContent = `Cube ${index + 1}:`;

        const scrambleText = document.createElement("div");
        scrambleText.className = "mbld-solve-details-scramble-text";
        scrambleText.textContent = scramble;

        scrambleItem.appendChild(scrambleHeader);
        scrambleItem.appendChild(scrambleText);
        scramblesContainer.appendChild(scrambleItem);
      });

      solveDetailsScramble.appendChild(scramblesContainer);
    } else if (solve.isFMC) {
      // For FMC, show both scramble and solution
      solveDetailsScramble.innerHTML = "";

      // Create a container
      const fmcContainer = document.createElement("div");
      fmcContainer.className = "fmc-details-container";

      // Add scramble
      const scrambleSection = document.createElement("div");
      scrambleSection.className = "fmc-details-section";

      const scrambleHeader = document.createElement("div");
      scrambleHeader.className = "fmc-details-header";

      // Get translations
      const translations = window.i18nModule?.translations || {};
      const fmcTranslations = translations.fmc || {};

      scrambleHeader.textContent = fmcTranslations.scramble || "Scramble:";

      const scrambleText = document.createElement("div");
      scrambleText.className = "fmc-details-scramble";
      scrambleText.textContent = solve.scramble || "No scramble available";

      scrambleSection.appendChild(scrambleHeader);
      scrambleSection.appendChild(scrambleText);
      fmcContainer.appendChild(scrambleSection);

      // Add solution if available
      if (solve.solution) {
        const solutionSection = document.createElement("div");
        solutionSection.className = "fmc-details-section";

        const solutionHeader = document.createElement("div");
        solutionHeader.className = "fmc-details-header";
        solutionHeader.textContent = fmcTranslations.solution || "Solution:";

        const solutionText = document.createElement("div");
        solutionText.className = "fmc-details-solution";
        solutionText.textContent = solve.solution;

        const moveCount = document.createElement("div");
        moveCount.className = "fmc-details-move-count";
        moveCount.textContent = `${fmcTranslations.moveCount || "Moves:"} ${
          solve.moveCount
        }`;

        solutionSection.appendChild(solutionHeader);
        solutionSection.appendChild(solutionText);
        solutionSection.appendChild(moveCount);
        fmcContainer.appendChild(solutionSection);
      }

      solveDetailsScramble.appendChild(fmcContainer);
    } else {
      // Regular event - single scramble
      solveDetailsScramble.textContent =
        solve.scramble || "No scramble available";
    }

    // Update scramble title based on whether it was edited
    // Find the scramble title element by looking for the scramble section specifically
    const scrambleSection = document.querySelector(
      "#solve-details-modal .solve-details-section"
    );
    const scrambleTitle = scrambleSection
      ? scrambleSection.querySelector(".solve-details-section-title")
      : null;

    if (scrambleTitle) {
      // Get translations
      const translations = window.i18nModule?.translations || {};
      const solveDetailsTranslations = translations.solveDetails || {};

      // Always set the correct title and data-i18n attribute based on the specific solve
      if (solve.isScrambleEdited) {
        scrambleTitle.textContent =
          solveDetailsTranslations.editedScramble || "Edited Scramble";
        scrambleTitle.setAttribute("data-i18n", "solveDetails.editedScramble");
      } else {
        scrambleTitle.textContent =
          solveDetailsTranslations.scramble || "Scramble";
        scrambleTitle.setAttribute("data-i18n", "solveDetails.scramble");
      }
    }

    // Add event-specific class to scramble element for proper font sizing
    solveDetailsScramble.className = "solve-details-scramble";
    solveDetailsScramble.classList.add(`event-${currentEvent}`);

    // Set comment
    solveDetailsComment.value = solve.comment || "";

    // Set penalty radio - different handling for MBLD and FMC
    if (solve.isMBLD) {
      // Disable all penalty radios for MBLD
      penaltyRadios.forEach((radio) => {
        radio.disabled = true;
        radio.checked = radio.value === "none";
      });
    } else if (solve.isFMC) {
      // For FMC, only allow DNF penalty (not +2)
      penaltyRadios.forEach((radio) => {
        // Disable +2 penalty for FMC, but allow DNF
        if (radio.value === "+2") {
          radio.disabled = true;
        } else {
          radio.disabled = false;
        }

        // Set the correct penalty
        let penaltyValue = "none";
        if (solve.penalty === "DNF" || solve.isDNF) {
          penaltyValue = "dnf";
        }

        radio.checked = radio.value === penaltyValue;
      });
    } else {
      // Enable all penalty radios for regular solves
      penaltyRadios.forEach((radio) => {
        radio.disabled = false;
      });

      // Set the correct penalty
      let penaltyValue = "none";
      if (solve.penalty === "+2") {
        penaltyValue = "+2";
      } else if (solve.penalty === "DNF") {
        penaltyValue = "dnf";
      }

      penaltyRadios.forEach((radio) => {
        radio.checked = radio.value === penaltyValue;
      });
    }

    // Show modal
    solveDetailsModal.classList.add("show");
  }

  // Close solve details modal
  solveDetailsClose.addEventListener("click", function () {
    solveDetailsModal.classList.remove("show");
    currentSolveIndex = -1;
  });

  // Share solve details
  solveDetailsShare.addEventListener("click", function () {
    if (currentSolveIndex === -1) return;

    // Determine the storage key based on whether we're in a custom session or not
    const storageKey = currentSessionId
      ? `session_${currentSessionId}`
      : currentEvent;

    const solve = timesMap[storageKey][currentSolveIndex];
    if (solve) {
      // Determine share type - check if this is a PB
      const allTimes = timesMap[storageKey];
      const validTimes = allTimes.filter((t) => t.time !== Infinity);
      const isPB =
        validTimes.length > 0 &&
        solve.time === Math.min(...validTimes.map((t) => t.time));

      const shareType = isPB ? "pb" : "normal";
      generateShareImage(solve, shareType);
    }
  });

  // Copy scramble button functionality
  const copyScrambleBtn = document.getElementById("copy-scramble-btn");
  if (copyScrambleBtn) {
    copyScrambleBtn.addEventListener("click", function () {
      const scrambleElement = document.getElementById("solve-details-scramble");
      if (scrambleElement && scrambleElement.textContent) {
        navigator.clipboard
          .writeText(scrambleElement.textContent.trim())
          .then(() => {
            // Visual feedback - temporarily change icon
            const icon = copyScrambleBtn.querySelector("i");
            if (icon) {
              icon.classList.remove("fa-copy");
              icon.classList.add("fa-check");
              setTimeout(() => {
                icon.classList.remove("fa-check");
                icon.classList.add("fa-copy");
              }, 1000);
            }
          })
          .catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement("textarea");
            textArea.value = scrambleElement.textContent.trim();
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand("copy");
            document.body.removeChild(textArea);

            // Visual feedback
            const icon = copyScrambleBtn.querySelector("i");
            if (icon) {
              icon.classList.remove("fa-copy");
              icon.classList.add("fa-check");
              setTimeout(() => {
                icon.classList.remove("fa-check");
                icon.classList.add("fa-copy");
              }, 1000);
            }
          });
      }
    });
  }

  // Main scramble box gesture functionality (moved to global scope)
  window.setupScrambleGestures = function setupScrambleGestures() {
    const scrambleElement = document.getElementById("scramble");
    if (!scrambleElement) return;

    let tapCount = 0;
    let tapTimer = null;
    let holdTimer = null;
    let isHolding = false;
    let isEditing = false;

    // Function to copy main scramble
    function copyMainScramble() {
      const scrambleText = document.getElementById("scramble-text");
      if (scrambleText && scrambleText.textContent && currentScramble) {
        navigator.clipboard
          .writeText(currentScramble.trim())
          .then(() => {
            // Get translations
            const translations = window.i18nModule?.translations || {};
            const gestureTranslations = translations.gestures || {};
            showGestureFeedback(
              gestureTranslations.scrambleCopied || "Scramble copied"
            );
          })
          .catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement("textarea");
            textArea.value = currentScramble.trim();
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand("copy");
            document.body.removeChild(textArea);

            // Get translations
            const translations = window.i18nModule?.translations || {};
            const gestureTranslations = translations.gestures || {};
            showGestureFeedback(
              gestureTranslations.scrambleCopied || "Scramble copied"
            );
          });
      }
    }

    // Function to start editing scramble
    function startEditingScramble() {
      // Don't allow editing for FMC or MBLD
      if (currentEvent === "333fm" || currentEvent === "333mbf") {
        return;
      }

      isEditing = true;
      const scrambleText = document.getElementById("scramble-text");
      if (!scrambleText) return;

      // Get the current dimensions of the scramble element
      const scrambleRect = scrambleElement.getBoundingClientRect();
      const scrambleStyles = window.getComputedStyle(scrambleElement);

      // Create textarea element (better for multi-line scrambles)
      const input = document.createElement("textarea");
      input.value = currentScramble || "";
      input.className = "scramble-edit-input";
      input.style.cssText = `
        width: 100%;
        height: ${scrambleRect.height}px;
        min-height: ${scrambleRect.height}px;
        background-color: var(--card-bg);
        border: 2px dashed var(--accent-color);
        color: var(--text-color);
        font-family: monospace;
        font-size: ${scrambleStyles.fontSize};
        text-align: center;
        padding: ${scrambleStyles.padding};
        border-radius: 8px;
        outline: none;
        resize: none;
        overflow: hidden;
        line-height: ${scrambleStyles.lineHeight};
        box-shadow: 0 2px 5px var(--shadow-color);
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
      `;

      // Replace scramble text with input
      scrambleText.style.display = "none";
      scrambleElement.appendChild(input);
      input.focus();
      input.select();

      // Function to finish editing
      function finishEditing() {
        if (!isEditing) return;
        isEditing = false;

        const newScramble = input.value.trim();
        input.remove();
        scrambleText.style.display = "inline";

        if (newScramble && newScramble !== currentScramble) {
          // Update the scramble
          currentScramble = newScramble;
          scrambleText.textContent = newScramble;

          // Mark scramble as edited for FUTURE solves only
          window.isScrambleEdited = true;

          // Update visualization if available
          updateVisualization();
        }
      }

      // Handle input events
      input.addEventListener("blur", finishEditing);
      input.addEventListener("keydown", (e) => {
        if (e.key === "Enter") {
          finishEditing();
        } else if (e.key === "Escape") {
          isEditing = false;
          input.remove();
          scrambleText.style.display = "inline";
        }
      });
    }

    // Touch/click event handlers
    function handleStart(e) {
      if (isEditing) return;

      // Don't handle if clicking on MBLD scramble
      if (scrambleElement.classList.contains("mbld-clickable")) {
        return;
      }

      // Disable gestures during inspection or timer running
      if (
        timerState === TIMER_STATE.INSPECTION ||
        timerState === TIMER_STATE.RUNNING ||
        timerState === TIMER_STATE.HOLDING ||
        timerState === TIMER_STATE.READY
      ) {
        return;
      }

      // Stop event propagation to prevent container handlers from interfering
      e.preventDefault();
      e.stopPropagation();

      tapCount++;

      // Clear existing timers
      if (tapTimer) {
        clearTimeout(tapTimer);
      }
      if (holdTimer) {
        clearTimeout(holdTimer);
      }

      // Start hold timer - only trigger if still holding when timer expires
      isHolding = false;
      holdTimer = setTimeout(() => {
        // Only start editing if we're still in a "holding" state (not released yet)
        if (!isHolding) {
          isHolding = true;
          startEditingScramble();
          tapCount = 0;
          // Clear tap timer since we're now holding
          if (tapTimer) {
            clearTimeout(tapTimer);
            tapTimer = null;
          }
        }
      }, 500); // 500ms hold time

      // Don't set tap timer here - wait for handleEnd to process taps
    }

    function handleEnd(e) {
      // Stop event propagation to prevent container handlers from interfering
      e.preventDefault();
      e.stopPropagation();

      // If we release before the hold timer expires, cancel the hold
      if (holdTimer && !isHolding) {
        clearTimeout(holdTimer);
        holdTimer = null;

        // Now process tap gestures since we didn't hold
        if (tapTimer) {
          clearTimeout(tapTimer);
        }

        // Set tap timer for double tap detection
        tapTimer = setTimeout(() => {
          if (tapCount === 1) {
            // Single tap - do nothing special
          } else if (tapCount >= 2) {
            // Double tap - copy scramble
            copyMainScramble();
          }
          tapCount = 0;
        }, 300); // 300ms double tap window
      }

      // If we were holding and now released, don't process taps
      if (isHolding) {
        tapCount = 0;
        if (tapTimer) {
          clearTimeout(tapTimer);
          tapTimer = null;
        }
        isHolding = false; // Reset holding state
      }
    }

    function handleCancel(e) {
      // Clear all timers on cancel (like when scrolling starts)
      if (holdTimer) {
        clearTimeout(holdTimer);
        holdTimer = null;
      }
      if (tapTimer) {
        clearTimeout(tapTimer);
        tapTimer = null;
      }
      tapCount = 0;
      isHolding = false;
    }

    // Add event listeners
    scrambleElement.addEventListener("mousedown", handleStart);
    scrambleElement.addEventListener("mouseup", handleEnd);
    scrambleElement.addEventListener("mouseleave", handleCancel); // Cancel on mouse leave

    scrambleElement.addEventListener("touchstart", handleStart, {
      passive: false, // Allow preventDefault
    });
    scrambleElement.addEventListener("touchend", handleEnd, { passive: false }); // Allow preventDefault
    scrambleElement.addEventListener("touchcancel", handleCancel, {
      passive: true,
    });

    // Prevent context menu on long press
    scrambleElement.addEventListener("contextmenu", (e) => {
      if (isHolding || isEditing) {
        e.preventDefault();
      }
    });

    // Add visual feedback for hold gesture
    scrambleElement.addEventListener("mousedown", (e) => {
      if (!isEditing && !scrambleElement.classList.contains("mbld-clickable")) {
        scrambleElement.style.transition = "background-color 0.5s ease";
        setTimeout(() => {
          if (!isHolding && !isEditing) {
            scrambleElement.style.backgroundColor = "#e0e0e0";
          }
        }, 200);
      }
    });

    scrambleElement.addEventListener("mouseup", (e) => {
      if (!isHolding && !isEditing) {
        scrambleElement.style.backgroundColor = "";
        scrambleElement.style.transition = "";
      }
    });

    scrambleElement.addEventListener("mouseleave", (e) => {
      if (!isHolding && !isEditing) {
        scrambleElement.style.backgroundColor = "";
        scrambleElement.style.transition = "";
      }
    });
  };

  // Initialize scramble gestures
  window.setupScrambleGestures();

  // Save solve details
  solveDetailsSave.addEventListener("click", function () {
    if (currentSolveIndex === -1) return;

    // Determine the storage key based on whether we're in a custom session or not
    const storageKey = currentSessionId
      ? `session_${currentSessionId}`
      : currentEvent;

    const solve = timesMap[storageKey][currentSolveIndex];

    // Save comment
    solve.comment = solveDetailsComment.value;

    // Save penalty
    let selectedPenalty = null;
    penaltyRadios.forEach((radio) => {
      if (radio.checked) {
        if (radio.value === "+2") {
          selectedPenalty = "+2";
        } else if (radio.value === "dnf") {
          selectedPenalty = "DNF";
        }
      }
    });

    // Apply penalty - special handling for FMC
    if (solve.isFMC) {
      // For FMC, we only handle DNF penalties
      if (selectedPenalty === "DNF" && !solve.isDNF && !solve.penalty) {
        // Mark as DNF
        solve.isDNF = true;
        solve.penalty = "DNF";
        solve.result = "DNF";
        solve.time = Infinity;

        // Store the original move count
        if (typeof solve.moveCount === "number") {
          solve.originalMoveCount = solve.moveCount;
        }

        // Set DNF reason if not already set
        if (!solve.dnfReason) {
          solve.dnfReason = "Manually marked as DNF";
        }
      } else if (
        selectedPenalty === null &&
        (solve.isDNF || solve.penalty === "DNF")
      ) {
        // Remove DNF penalty
        solve.isDNF = false;
        solve.penalty = null;

        // Restore original move count if available
        if (typeof solve.originalMoveCount === "number") {
          solve.moveCount = solve.originalMoveCount;
          solve.time = solve.moveCount;
          solve.result = solve.moveCount.toString();
        }

        // Clear DNF reason
        delete solve.dnfReason;
      }
    } else {
      // Regular events - apply penalty if it changed
      if (selectedPenalty !== solve.penalty) {
        // Store the raw time without any penalties for reference
        let rawTime = solve.time;

        // If current penalty is +2, get the raw time by removing the penalty
        if (solve.penalty === "+2") {
          rawTime = solve.time - 2000; // Remove 2 seconds to get raw time
        }
        // If current penalty is DNF, use the stored original time as the raw time
        else if (
          solve.penalty === "DNF" &&
          solve.originalTime &&
          isFinite(solve.originalTime)
        ) {
          rawTime = solve.originalTime;
        }

        // Apply new penalty
        if (selectedPenalty === "+2") {
          // Use the raw time we calculated earlier and add the +2 penalty
          solve.time = rawTime + 2000; // Add 2 seconds to raw time
          solve.penalty = "+2";
          solve.result = formatTime(solve.time) + " (+2)";

          // We can clear the originalTime since we're no longer a DNF
          delete solve.originalTime;
        } else if (selectedPenalty === "DNF") {
          // Store the raw time as the original time
          solve.originalTime = rawTime;

          solve.penalty = "DNF";
          solve.result = "DNF";
          solve.time = Infinity;
        } else {
          // No penalty - use the raw time directly
          solve.time = rawTime;
          solve.penalty = null;
          solve.result = null; // Reset result to use the raw time

          // Clear the originalTime since we're no longer a DNF
          delete solve.originalTime;
        }
      }
    }

    // Update UI and save
    updateTimesList();
    updateStats();
    saveTimes();

    // Close modal
    solveDetailsModal.classList.remove("show");
    currentSolveIndex = -1;
  });

  // Close modal when clicking outside
  document.addEventListener("click", function (event) {
    if (
      solveDetailsModal.classList.contains("show") &&
      !event.target.closest(".solve-details-content") &&
      !event.target.closest(".time-entry")
    ) {
      solveDetailsModal.classList.remove("show");
      currentSolveIndex = -1;
    }
  });

  // Update statistics
  function updateStats() {
    // Determine the storage key based on whether we're in a custom session or not
    const storageKey = currentSessionId
      ? `session_${currentSessionId}`
      : currentEvent;

    const times = timesMap[storageKey] || [];

    // First, ensure the stats container has the correct structure for the current event
    const statsContainer = document.querySelector(".stats-container");
    if (statsContainer) {
      // Check if we need to restore the default stats structure
      // This is needed when switching from FMC/MBLD to regular events
      const needsDefaultStructure =
        statsContainer.querySelector(".stats") === null ||
        statsContainer.classList.contains("fmc-stats") ||
        statsContainer.classList.contains("mbld-stats");

      if (
        currentEvent !== "333fm" &&
        currentEvent !== "333mbf" &&
        needsDefaultStructure
      ) {
        // Restoring default stats structure for regular event

        // Restore the original stats HTML structure
        statsContainer.innerHTML = `
          <div class="stats">
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.solves">Solves:</div>
              <div class="stat-value" id="solve-count">0</div>
            </div>
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.best">Best:</div>
              <div class="stat-value" id="best-time">-</div>
            </div>
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.mean">Mean:</div>
              <div class="stat-value" id="mean-time">-</div>
            </div>
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.avg5">ao5:</div>
              <div class="stat-value" id="avg5">-</div>
            </div>
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.avg12">ao12:</div>
              <div class="stat-value" id="avg12">-</div>
            </div>
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.avg100">ao100:</div>
              <div class="stat-value" id="avg100">-</div>
            </div>
          </div>
        `;

        // Remove any FMC or MBLD specific classes
        statsContainer.classList.remove("fmc-stats", "mbld-stats");

        // Make sure the stats container is visible
        statsContainer.style.display = "block";
        statsContainer.classList.remove("hidden");

        // Re-get the DOM elements after rebuilding the structure
        solveCountElement = document.getElementById("solve-count");
        bestTimeElement = document.getElementById("best-time");
        meanTimeElement = document.getElementById("mean-time");
        avg5Element = document.getElementById("avg5");
        avg12Element = document.getElementById("avg12");
        avg100Element = document.getElementById("avg100");
      }
    }

    // Update solve count
    if (solveCountElement) {
      solveCountElement.textContent = times.length;
    }

    // Handle different event types differently
    if (currentEvent === "333fm") {
      // For FMC, use the FMC-specific stats display function if available
      if (typeof setupFMCStatsDisplay === "function") {
        setupFMCStatsDisplay();
        return; // Exit early as FMC has its own stats display
      } else {
        // Try to import the FMC stats display function
        import("./fmc-manager.js")
          .then(({ setupFMCStatsDisplay }) => {
            if (typeof setupFMCStatsDisplay === "function") {
              setupFMCStatsDisplay();
            } else {
              // Fallback to standard stats if function not available
              updateRegularStats(times);
            }
          })
          .catch((error) => {
            // Failed to import setupFMCStatsDisplay
            // Fallback to standard stats
            updateRegularStats(times);
          });
        return;
      }
    } else if (currentEvent === "333mbf") {
      // For MBLD, use the MBLD-specific stats display function if available
      if (typeof updateMBLDStatsDisplay === "function") {
        updateMBLDStatsDisplay();
        return; // Exit early as MBLD has its own stats display
      } else {
        // Try to import the MBLD stats display function
        import("./mbld-manager.js")
          .then(({ updateMBLDStatsDisplay }) => {
            if (typeof updateMBLDStatsDisplay === "function") {
              updateMBLDStatsDisplay();
            } else {
              // Fallback to standard stats if function not available
              updateRegularStats(times);
            }
          })
          .catch((error) => {
            // Failed to import updateMBLDStatsDisplay
            // Fallback to standard stats
            updateRegularStats(times);
          });
        return;
      }
    } else {
      // Regular events use standard stats calculation
      updateRegularStats(times);
    }
  }

  // Helper function for regular events statistics
  function updateRegularStats(times) {
    // Best time
    if (times.length > 0) {
      const bestTime = Math.min(...times.map((t) => t.time));
      bestTimeElement.textContent = formatTime(bestTime);
    } else {
      bestTimeElement.textContent = "-";
    }

    // Mean (average of all times)
    if (times.length > 0) {
      // Filter out DNFs (Infinity values) for mean calculation
      const validTimes = times.filter((t) => t.time !== Infinity);
      if (validTimes.length > 0) {
        const sum = validTimes.reduce((total, t) => total + t.time, 0);
        const mean = sum / validTimes.length;
        meanTimeElement.textContent = formatTime(mean);
      } else {
        meanTimeElement.textContent = "DNF";
      }
    } else {
      meanTimeElement.textContent = "-";
    }

    // Average of 5
    if (times.length >= 5) {
      const avg5 = calculateAverage(times.slice(0, 5));
      avg5Element.textContent = isFinite(avg5) ? formatTime(avg5) : "DNF";
    } else {
      avg5Element.textContent = "-";
    }

    // Average of 12
    if (times.length >= 12) {
      const avg12 = calculateAverage(times.slice(0, 12));
      avg12Element.textContent = isFinite(avg12) ? formatTime(avg12) : "DNF";
    } else {
      avg12Element.textContent = "-";
    }

    // Average of 100
    if (times.length >= 100) {
      const avg100 = calculateAverage(times.slice(0, 100));
      avg100Element.textContent = isFinite(avg100) ? formatTime(avg100) : "DNF";
    } else {
      avg100Element.textContent = "-";
    }
  }

  // Calculate average (removing best and worst)
  function calculateAverage(times) {
    if (times.length < 3) return 0;

    // Check if there are more than one DNF
    const dnfCount = times.filter((t) => t.time === Infinity).length;
    if (dnfCount > 1) {
      // If more than one DNF, the average is DNF
      return Infinity;
    }

    // Sort times (DNFs will be at the end)
    const sortedTimes = [...times].sort((a, b) => a.time - b.time);

    // Remove best and worst time
    sortedTimes.shift(); // Remove best
    sortedTimes.pop(); // Remove worst (this will remove a DNF if there is one)

    // Check if any remaining times are DNF
    if (sortedTimes.some((t) => t.time === Infinity)) {
      return Infinity; // DNF
    }

    // Calculate average of remaining times
    const sum = sortedTimes.reduce((total, t) => total + t.time, 0);
    return sum / sortedTimes.length;
  }

  // Save times to localStorage
  function saveTimes() {
    localStorage.setItem("scTimer-times", JSON.stringify(timesMap));
  }

  // Load times from localStorage
  function loadTimes() {
    const savedTimes = localStorage.getItem("scTimer-times");
    timesMap = savedTimes ? JSON.parse(savedTimes) : {};

    // Determine the storage key based on whether we're in a custom session or not
    const storageKey = currentSessionId
      ? `session_${currentSessionId}`
      : currentEvent;

    if (!timesMap[storageKey]) {
      timesMap[storageKey] = [];
    }

    // Load custom sessions
    loadCustomSessions();

    updateTimesList();
    updateStats();
  }

  // Load custom sessions from localStorage
  function loadCustomSessions() {
    const savedSessions = localStorage.getItem("scTimer-customSessions");
    if (savedSessions) {
      try {
        customSessions = JSON.parse(savedSessions);
        // Add custom sessions to the dropdown
        updateCustomSessionsInDropdown();
      } catch (e) {
        // Error parsing saved custom sessions
        customSessions = [];
      }
    }
  }

  // Save custom sessions to localStorage
  function saveCustomSessions() {
    localStorage.setItem(
      "scTimer-customSessions",
      JSON.stringify(customSessions)
    );
  }

  // Update custom sessions in the dropdown
  function updateCustomSessionsInDropdown() {
    // Remove existing custom session options and dividers
    const existingCustomOptions = document.querySelectorAll(
      ".event-option.custom-session, .sessions-divider"
    );
    existingCustomOptions.forEach((option) => option.remove());

    // Get the event divider and new session option
    const eventDivider = document.querySelector(".event-divider");
    if (!eventDivider) return;

    // If there are no custom sessions, we don't need to add anything
    if (customSessions.length === 0) {
      return;
    }

    // Create a divider for custom sessions
    const sessionsHeaderDivider = document.createElement("div");
    sessionsHeaderDivider.className = "event-divider sessions-divider";

    // Insert the sessions divider before the new session divider
    eventDivider.parentNode.insertBefore(sessionsHeaderDivider, eventDivider);

    // Add custom sessions after the sessions divider
    customSessions.forEach((session) => {
      const sessionOption = document.createElement("div");
      sessionOption.className = "event-option custom-session";
      sessionOption.setAttribute("data-event", session.puzzleType);
      sessionOption.setAttribute("data-session-id", session.id);
      sessionOption.setAttribute("data-session-name", session.name);

      // Create icon span
      const iconSpan = document.createElement("span");

      // Set the appropriate icon class
      // For non-WCA puzzles, use the unofficial- prefix for icons only
      const nonWcaPuzzleIcons = {
        fto: "unofficial-fto",
        master_tetraminx: "unofficial-mtetram",
        kilominx: "unofficial-kilominx",
        redi_cube: "unofficial-redi",
        baby_fto: "unofficial-baby_fto",
      };

      if (nonWcaPuzzleIcons[session.puzzleType] !== undefined) {
        // For non-WCA puzzles with icons, use the unofficial- prefix
        if (nonWcaPuzzleIcons[session.puzzleType]) {
          iconSpan.className = `cubing-icon ${
            nonWcaPuzzleIcons[session.puzzleType]
          }`;
        } else {
          // For puzzles without icons, just use a generic icon or none
          iconSpan.className = ""; // No icon
        }
      } else {
        // For WCA events, use the event- prefix
        iconSpan.className = `cubing-icon event-${session.puzzleType}`;
      }

      sessionOption.appendChild(iconSpan);

      // Create text span
      const textSpan = document.createElement("span");
      textSpan.textContent = session.name;
      sessionOption.appendChild(textSpan);

      // Add click event listener
      sessionOption.addEventListener("click", function () {
        const eventId = this.getAttribute("data-event");
        const sessionId = this.getAttribute("data-session-id");
        const sessionName = this.getAttribute("data-session-name");

        // Update button text and icon
        currentEventText.textContent = sessionName;

        // Set the appropriate icon class
        // For non-WCA puzzles, use the unofficial- prefix for icons only
        const nonWcaPuzzleIcons = {
          fto: "unofficial-fto",
          master_tetraminx: "unofficial-mtetram",
          kilominx: "unofficial-kilominx",
          redi_cube: "unofficial-redi",
          baby_fto: "unofficial-baby_fto",
        };

        if (nonWcaPuzzleIcons[eventId] !== undefined) {
          // For non-WCA puzzles with icons, use the unofficial- prefix
          if (nonWcaPuzzleIcons[eventId]) {
            currentEventIcon.className = `cubing-icon ${nonWcaPuzzleIcons[eventId]}`;
          } else {
            // For puzzles without icons, just use a generic icon or none
            currentEventIcon.className = ""; // No icon
          }
        } else {
          // For WCA events, use the event- prefix
          currentEventIcon.className = `cubing-icon event-${eventId}`;
        }

        // Update current event
        currentEvent = eventId;
        currentEventElement.textContent = eventId;

        // Store the current session ID
        currentSessionId = sessionId;

        // Close dropdown
        eventDropdown.classList.remove("show");

        // Update FMC keyboard label state
        updateFMCKeyboardLabelState();

        // Process the event change
        processEventChange(eventId, sessionName);

        // Save the current event and session state
        saveCurrentEventAndSession();
      });

      // Insert before the new session divider
      eventDivider.parentNode.insertBefore(sessionOption, eventDivider);
    });
  }

  // Update the visualization - exported so it can be called from MBLD manager
  window.updateVisualization = function updateVisualization() {
    if (!showVisualizationCheckbox.checked) return;

    // Skip visualization for FMC, but don't return
    // This allows the visualization to be properly set up for other events
    if (currentEvent === "333fm") {
      visualizationContainer.style.display = "none";
    } else {
      visualizationContainer.style.display = "";
    }

    // Clear previous visualization
    visualizationContainer.innerHTML = "";

    // For MBLD, create a clickable visualization that shows all scrambles
    if (currentEvent === "333mbf") {
      // Get translations
      const translations = window.i18nModule?.translations || {};
      const mbldTranslations = translations.mbld || {};

      // Create a clickable container
      const clickableContainer = document.createElement("div");
      clickableContainer.className = "mbld-visualization-container";
      clickableContainer.innerHTML = `<div class="mbld-visualization-text">${
        mbldTranslations.clickToView ||
        "Click to view all cube visualizations & scrambles"
      }</div>`;

      // Add multiple event listeners to ensure clicks are captured
      clickableContainer.addEventListener("click", function (e) {
        e.stopPropagation(); // Stop event from bubbling up
        showMBLDVisualizations();
      });

      clickableContainer.addEventListener("mousedown", function (e) {
        e.stopPropagation(); // Stop event from bubbling up
      });

      clickableContainer.addEventListener("touchstart", function (e) {
        e.stopPropagation(); // Stop event from bubbling up
        e.preventDefault(); // Prevent default touch behavior
      });

      // Add touchend event for mobile devices
      clickableContainer.addEventListener("touchend", function (e) {
        e.stopPropagation(); // Stop event from bubbling up
        e.preventDefault(); // Prevent default touch behavior
        // Call the visualization function on touchend for mobile devices
        showMBLDVisualizations();
      });

      visualizationContainer.appendChild(clickableContainer);

      // Make the visualization container clickable
      visualizationContainer.classList.add("clickable");
      return;
    }

    // Remove clickable class if not MBLD
    visualizationContainer.classList.remove("clickable");

    // Map event codes to twisty.js puzzle IDs
    const puzzleMap = {
      // WCA puzzles
      222: "2x2x2",
      333: "3x3x3",
      "333bf": "3x3x3",
      "333fm": "3x3x3",
      "333oh": "3x3x3",
      444: "4x4x4",
      "444bf": "4x4x4",
      555: "5x5x5",
      "555bf": "5x5x5",
      666: "6x6x6",
      777: "7x7x7",
      clock: "clock",
      minx: "megaminx",
      pyram: "pyraminx",
      skewb: "skewb",
      sq1: "square1",
      // Non-WCA puzzles - use correct puzzle IDs for visualization
      fto: "fto", // Supported by cubing.js
      master_tetraminx: "master_tetraminx", // Supported by cubing.js
      kilominx: "kilominx", // Supported by cubing.js
      redi_cube: "redi_cube", // Supported by cubing.js
      baby_fto: "baby_fto", // baby_fto has its own visualization
    };

    const puzzleId = puzzleMap[currentEvent] || "3x3x3";

    try {
      // Clean up any existing twisty player
      if (twistyPlayer) {
        try {
          twistyPlayer.remove();
        } catch (e) {
          // Error removing existing twisty player
        }
      }

      // Create a new twisty player with 2D visualization
      twistyPlayer = new TwistyPlayer({
        puzzle: puzzleId,
        alg: "",
        experimentalSetupAlg: currentScramble,
        experimentalSetupAnchor: "start",
        background: "none",
        controlPanel: "none",
        visualization: "2D",
      });

      // Set the size using CSS
      twistyPlayer.style.width = "180px";
      twistyPlayer.style.height = "180px";

      visualizationContainer.appendChild(twistyPlayer);
    } catch (error) {
      // Error creating visualization

      // No special fallback needed for baby_fto as it has its own visualization

      // Show a simple message instead of visualization if all attempts fail
      visualizationContainer.innerHTML =
        '<div style="color: var(--text-color); text-align: center; padding: 20px;">' +
        '<i class="fas fa-cube" style="font-size: 48px; margin-bottom: 10px;"></i><br>' +
        "Visualization unavailable</div>";
    }
  };

  // Also make updateVisualization available as a regular function in this scope
  const updateVisualization = window.updateVisualization;

  // Generate a WCA scramble
  async function generateScramble() {
    // Check if we're already generating a scramble
    if (window.isGeneratingScramble) {
      return currentScramble;
    }

    // Set flag to prevent multiple simultaneous scramble generations
    window.isGeneratingScramble = true;

    // Get scramble elements with null checks
    const scrambleLoader = document.getElementById("scramble-loader");
    const scrambleText = document.getElementById("scramble-text");
    const scrambleElement = document.getElementById("scramble");

    // Show loader and hide text if elements exist
    if (scrambleLoader) {
      scrambleLoader.style.display = "inline-block";
    }

    if (scrambleText) {
      scrambleText.classList.add("hidden");
      // Clear the scramble text to ensure it's updated
      scrambleText.textContent = "";
    }

    // Update current event display if element exists
    if (currentEventElement) {
      currentEventElement.textContent = currentEvent;
    }

    // Update scramble element class based on current event
    if (scrambleElement) {
      // Remove all event classes
      scrambleElement.className = "scramble";

      // Add event-specific class
      scrambleElement.classList.add(`event-${currentEvent}`);

      // For FMC, hide the scramble initially
      if (currentEvent === "333fm") {
        scrambleElement.style.visibility = "hidden";
      } else {
        // Make sure scramble is visible for non-FMC events
        scrambleElement.style.visibility = "visible";
      }
    }

    // Reset current scramble to ensure it's updated
    currentScramble = "";

    try {
      // For FMC, use the FMC module directly instead of this function
      if (currentEvent === "333fm") {
        // Import the FMC module and use its scramble generator
        try {
          const fmcModule = await import("./fmc-manager.js");
          const scramble = await fmcModule.generateFMCScramble();

          // Update the current scramble
          currentScramble = scramble;

          // Reset the flag
          window.isGeneratingScramble = false;

          // Get scramble elements with null checks
          const scrambleLoader = document.getElementById("scramble-loader");
          const scrambleText = document.getElementById("scramble-text");

          // Hide loader and show text with scramble if elements exist
          if (scrambleLoader) {
            scrambleLoader.style.display = "none";
          }

          if (scrambleText) {
            scrambleText.textContent = scramble;
            // Keep hidden for FMC until attempt starts
            scrambleText.classList.add("hidden");
          }

          return scramble;
        } catch (fmcError) {
          // Error importing FMC module - continue with regular scramble generation as fallback
        }
      }

      // Map non-WCA puzzles to their scramble event IDs
      let scrambleEvent = currentEvent;

      // Remove "unofficial-" prefix if present (for scramble generation)
      if (scrambleEvent.startsWith("unofficial-")) {
        scrambleEvent = scrambleEvent.replace("unofficial-", "");
        // Removed unofficial- prefix for scramble generation
      }

      // For puzzles that need special handling - map to correct cubing.js event IDs
      const nonWcaPuzzles = {
        // Map puzzle types to their correct cubing.js event IDs
        fto: "fto", // FTO uses "fto" event ID
        master_tetraminx: "master_tetraminx", // Master Tetraminx uses "master_tetraminx" event ID
        kilominx: "kilominx", // Kilominx uses "kilominx" event ID
        redi_cube: "redi_cube", // Redi Cube uses "redi_cube" event ID
        baby_fto: "baby_fto", // Baby FTO uses "baby_fto" event ID for scrambles
      };

      // If it's a non-WCA puzzle that needs special handling, use the mapped event
      if (nonWcaPuzzles[scrambleEvent]) {
        scrambleEvent = nonWcaPuzzles[scrambleEvent];
        // Using mapped scramble event
      }

      // Use cubing.js for all scramble generation
      // Try to generate a scramble using cubing.js
      // Wrap in a timeout to prevent hanging if the module fails to load
      // Use a longer timeout for FMC (10 seconds) since it can take longer
      // Use longer timeouts to ensure WCA official scrambles are generated
      const timeout = scrambleEvent === "333fm" ? 15000 : 10000;

      // Suppress console logs during scramble generation
      const originalConsoleLog = console.log;
      console.log = () => {};

      const scramblePromise = Promise.race([
        randomScrambleForEvent(scrambleEvent).finally(() => {
          console.log = originalConsoleLog;
        }),
        new Promise((_, reject) =>
          setTimeout(() => {
            console.log = originalConsoleLog;
            reject(new Error("Scramble generation timed out"));
          }, timeout)
        ),
      ]);

      const scramble = await scramblePromise;

      // Check if the scramble was generated successfully
      if (!scramble) {
        throw new Error("Failed to generate scramble");
      }

      // Get the scramble as a string
      let scrambleString = scramble.toString();

      // No special handling needed for baby_fto - cubing.js handles it correctly

      // Update the current scramble
      currentScramble = scrambleString;

      // Reset edited flag for new scramble
      window.isScrambleEdited = false;

      // Generated scramble

      // Get scramble elements with null checks (again, in case they changed)
      const scrambleLoader = document.getElementById("scramble-loader");
      const scrambleText = document.getElementById("scramble-text");

      // Hide loader and show text with scramble if elements exist
      if (scrambleLoader) {
        scrambleLoader.style.display = "none";
      }

      if (scrambleText) {
        scrambleText.classList.remove("hidden");
        scrambleText.textContent = currentScramble;
        // Updated scramble text element
      }

      // Update scramble source if element exists
      if (scrambleSourceElement) {
        scrambleSourceElement.textContent = "WCA (cubing.js)";
      }

      // Update visualization
      updateVisualization();

      // Reset the flag to allow future scramble generations
      window.isGeneratingScramble = false;

      // Return the scramble
      return currentScramble;
    } catch (error) {
      // Error generating WCA scramble

      // Show error message instead of fallback
      const scrambleLoader = document.getElementById("scramble-loader");
      const scrambleText = document.getElementById("scramble-text");

      if (scrambleLoader) {
        scrambleLoader.style.display = "none";
      }

      if (scrambleText) {
        scrambleText.classList.remove("hidden");
        scrambleText.textContent =
          "Error: Unable to generate WCA scramble. Please refresh the page.";
        scrambleText.style.color = "#e74c3c";
      }

      // Update scramble source if element exists
      if (scrambleSourceElement) {
        scrambleSourceElement.textContent = "Error - WCA scramble failed";
      }

      // Reset the flag to allow future scramble generations
      window.isGeneratingScramble = false;

      // Return error message
      return "Error: Unable to generate WCA scramble";
    }
  }

  // Special function for generating kilominx scrambles with longer timeout
  async function generateKilominxScramble() {
    // Set flag to prevent multiple simultaneous scramble generations
    window.isGeneratingScramble = true;

    try {
      // Suppress console logs during kilominx scramble generation
      const originalConsoleLog = console.log;
      console.log = () => {};

      // First, try to generate a dummy scramble to initialize the scrambler
      // Use a longer timeout (10 seconds) for the first scramble
      const dummyPromise = Promise.race([
        randomScrambleForEvent("kilominx").finally(() => {
          console.log = originalConsoleLog;
        }),
        new Promise(
          (_, reject) =>
            setTimeout(() => {
              console.log = originalConsoleLog;
              reject(new Error("Kilominx initialization timed out"));
            }, 10000) // 10 seconds timeout for initialization
        ),
      ]);

      try {
        // Try to get the dummy scramble, but don't worry if it fails
        await dummyPromise;
        // Kilominx initialization successful
      } catch (initError) {
        // Kilominx initialization timed out, trying actual scramble anyway
        console.log = originalConsoleLog;
      }

      // Reset console suppression for actual scramble
      console.log = () => {};

      // Now generate the actual scramble with a longer timeout (8 seconds)
      const scramblePromise = Promise.race([
        randomScrambleForEvent("kilominx").finally(() => {
          console.log = originalConsoleLog;
        }),
        new Promise(
          (_, reject) =>
            setTimeout(() => {
              console.log = originalConsoleLog;
              reject(new Error("Kilominx scramble generation timed out"));
            }, 8000) // 8 seconds timeout for actual scramble
        ),
      ]);

      const scramble = await scramblePromise;

      // Check if the scramble was generated successfully
      if (!scramble) {
        throw new Error("Failed to generate kilominx scramble");
      }

      // Get the scramble as a string
      const scrambleString = scramble.toString();

      // Update the current scramble
      currentScramble = scrambleString;

      // Reset edited flag for new scramble
      window.isScrambleEdited = false;

      // Generated kilominx scramble

      // Reset the flag to allow future scramble generations
      window.isGeneratingScramble = false;

      // Return the scramble
      return currentScramble;
    } catch (error) {
      // Error generating kilominx scramble

      // Show error message instead of fallback
      const scrambleLoader = document.getElementById("scramble-loader");
      const scrambleText = document.getElementById("scramble-text");

      if (scrambleLoader) {
        scrambleLoader.style.display = "none";
      }

      if (scrambleText) {
        scrambleText.classList.remove("hidden");
        scrambleText.textContent =
          "Error: Unable to generate WCA kilominx scramble. Please refresh the page.";
        scrambleText.style.color = "#e74c3c";
      }

      // Reset the flag to allow future scramble generations
      window.isGeneratingScramble = false;

      // Return error message
      return "Error: Unable to generate WCA kilominx scramble";
    }
  }

  // Note: Only WCA official scrambles are used - no fallback scrambles

  // Common function for handling timer input start (both keyboard and touch)
  function handleTimerInputStart() {
    // Handle based on current timer state
    switch (timerState) {
      case TIMER_STATE.IDLE:
        if (useInspectionCheckbox.checked) {
          // Start inspection
          startInspection();
        } else {
          // Start holding without inspection
          spaceDownTime = Date.now();
          updateTimerState(TIMER_STATE.HOLDING);

          // Start tracking how long space/touch is held
          spaceHeldInterval = setInterval(function () {
            const heldTime = Date.now() - spaceDownTime;
            spaceHeldTimeElement.textContent = heldTime;

            // If held long enough, enter ready state
            if (
              heldTime >= HOLD_THRESHOLD &&
              timerState === TIMER_STATE.HOLDING
            ) {
              updateTimerState(TIMER_STATE.READY);
              clearInterval(spaceHeldInterval);
            }
          }, 10);
        }
        break;

      case TIMER_STATE.INSPECTION:
        // If we're already in inspection and press/touch again
        // Start tracking the input
        spaceDownTime = Date.now();
        updateTimerState(TIMER_STATE.HOLDING);

        // Start tracking how long input is held
        spaceHeldInterval = setInterval(function () {
          const heldTime = Date.now() - spaceDownTime;
          spaceHeldTimeElement.textContent = heldTime;

          // If held long enough, enter ready state
          if (
            heldTime >= HOLD_THRESHOLD &&
            timerState === TIMER_STATE.HOLDING
          ) {
            updateTimerState(TIMER_STATE.READY);
            clearInterval(spaceHeldInterval);
          }
        }, 10);
        break;

      case TIMER_STATE.RUNNING:
        // Stop the timer
        stopTimer();
        break;
    }
  }

  // Common function for handling timer input end (both keyboard and touch)
  function handleTimerInputEnd() {
    // Clear the held interval
    clearInterval(spaceHeldInterval);
    spaceHeldTimeElement.textContent = "0";

    // Handle based on current timer state
    switch (timerState) {
      case TIMER_STATE.READY:
        // Start the timer
        startTimer();
        break;

      case TIMER_STATE.HOLDING:
        // Check if we were in inspection before holding
        if (inspectionInterval) {
          // If we released during inspection holding, continue inspection
          updateTimerState(TIMER_STATE.INSPECTION);
        } else {
          // Released too early, go back to idle
          updateTimerState(TIMER_STATE.IDLE);

          // Remove inspection-active class if it was set
          document.body.classList.remove("inspection-active");
        }
        break;
    }
  }

  // Handle key down event (space bar press)
  document.addEventListener("keydown", function (event) {
    if (event.code !== "Space" || event.repeat) return;

    // Don't handle space if any modal is open (check this FIRST)
    const openModals = document.querySelectorAll(
      '[class*="modal"].show, .stats-details-panel.show'
    );
    if (openModals.length > 0) {
      return;
    }

    // Special handling for FMC mode
    if (currentEvent === "333fm") {
      // If FMC is not in progress, start a new attempt
      if (!isFMCInProgress()) {
        event.preventDefault();
        // Start FMC attempt
        startFMCAttempt();
        return;
      } else {
        // If FMC is in progress, prevent spacebar from triggering the timer
        event.preventDefault();
        return;
      }
    }

    // Special handling for manual input mode
    const isManualInputMode =
      timerModeSelector && timerModeSelector.value === "typing";

    // Special handling for Stackmat mode
    const isStackmatModeActive =
      timerModeSelector && timerModeSelector.value === "stackmat";

    // If we're in manual input mode, disable normal timer completely
    if (isManualInputMode) {
      event.preventDefault();

      // Start inspection if it's enabled and not already running
      if (
        useInspectionCheckbox &&
        useInspectionCheckbox.checked &&
        !inspectionInterval
      ) {
        startInspection();
      }
      return;
    }

    // If we're in Stackmat mode, only allow inspection
    if (isStackmatModeActive) {
      event.preventDefault();

      // Allow space to start inspection if inspection is enabled and timer is idle
      if (
        useInspectionCheckbox &&
        useInspectionCheckbox.checked &&
        timerState === TIMER_STATE.IDLE
      ) {
        startInspection();
      }
      return;
    }

    // Don't handle space if focus is in other text inputs or textareas
    // Special exception: allow space in FMC solution input only when in FMC event
    if (
      document.activeElement.tagName === "TEXTAREA" ||
      (document.activeElement.tagName === "INPUT" &&
        document.activeElement !== manualTimeInput &&
        !(
          currentEvent === "333fm" &&
          document.activeElement.classList.contains("fmc-solution-input")
        ))
    ) {
      return;
    }

    event.preventDefault();
    handleTimerInputStart();
  });

  // Handle key up event (space bar release)
  document.addEventListener("keyup", function (event) {
    if (event.code !== "Space") return;

    // Don't handle space if any modal is open (check this FIRST)
    const openModals = document.querySelectorAll(
      '[class*="modal"].show, .stats-details-panel.show'
    );
    if (openModals.length > 0) {
      return;
    }

    // Special handling for manual input mode
    const isManualInputMode =
      timerModeSelector && timerModeSelector.value === "typing";

    // Special handling for Stackmat mode
    const isStackmatModeActive =
      timerModeSelector && timerModeSelector.value === "stackmat";

    // If we're in manual input mode, disable normal timer completely
    if (isManualInputMode) {
      event.preventDefault();
      return;
    }

    // If we're in Stackmat mode, only allow inspection
    if (isStackmatModeActive) {
      event.preventDefault();
      // No action needed on keyup for Stackmat mode - inspection is started on keydown
      return;
    }

    // Don't handle space if focus is in other text inputs or textareas
    // Special exception: allow space in FMC solution input only when in FMC event
    if (
      document.activeElement.tagName === "TEXTAREA" ||
      (document.activeElement.tagName === "INPUT" &&
        document.activeElement !== manualTimeInput &&
        !(
          currentEvent === "333fm" &&
          document.activeElement.classList.contains("fmc-solution-input")
        ))
    ) {
      return;
    }

    event.preventDefault();
    handleTimerInputEnd();
  });

  // Mobile gesture detection variables
  let touchStartX = 0;
  let touchStartY = 0;
  let touchStartTime = 0;
  let gestureInProgress = false;

  // Gesture thresholds
  const SWIPE_THRESHOLD = 50; // Minimum distance for swipe
  const SWIPE_VELOCITY_THRESHOLD = 0.3; // Minimum velocity for swipe (pixels per ms)
  const GESTURE_TIMEOUT = 500; // Maximum time for gesture completion

  /**
   * Mobile Gestures Implementation:
   * - Swipe down: Delete most recent solve (with confirmation)
   * - Swipe up: Add/cycle penalty to most recent solve (no penalty -> +2 -> DNF -> no penalty)
   * - Swipe left/right: Generate new scramble
   *
   * Gestures are disabled when modals are open to prevent accidental actions.
   * Visual feedback is shown for all gesture actions.
   */

  // Handle touch/click events for the entire container
  const container = document.querySelector(".container");

  // Function to check if we should handle the touch/click
  function shouldHandleInput(event) {
    // Get the target element
    const target = event.target;

    // Check if clicking on scramble section or scramble area
    // This prevents inspection from being triggered when clicking on the scramble
    if (
      target.closest(".scramble-section") ||
      target.closest(".scramble") ||
      target.id === "scramble" ||
      target.id === "scramble-text" ||
      target.closest("#scramble")
    ) {
      return false;
    }

    // Don't handle if clicking on interactive elements
    if (
      target.tagName === "BUTTON" ||
      target.tagName === "INPUT" ||
      target.tagName === "SELECT" ||
      target.tagName === "LABEL" ||
      target.tagName === "TEXTAREA" ||
      target.closest(".event-selector") ||
      target.closest(".settings-modal") ||
      target.closest(".solve-details-modal") ||
      target.closest(".times-panel") ||
      target.closest(".times-list") ||
      target.closest(".time-entry") ||
      target.closest(".times-toggle") ||
      target.closest("header") ||
      target.closest(".header-controls") ||
      target.closest(".header-left") ||
      target.closest(".header-center") ||
      target.closest(".header-right") ||
      // We need to allow clicks on the visualization container for MBLD
      // but still prevent timer triggering when clicking on it
      (target.closest(".visualization") &&
        !target.closest(".mbld-visualization-container")) ||
      target.closest(".stats-container")
    ) {
      return false;
    }

    return true;
  }

  // Mobile gesture handlers
  function handleSwipeDown() {
    // Delete most recent solve
    const storageKey = currentSessionId
      ? `session_${currentSessionId}`
      : currentEvent;
    const times = timesMap[storageKey];

    if (!times || times.length === 0) {
      // Get translations
      const translations = window.i18nModule?.translations || {};
      const gestureTranslations = translations.gestures || {};
      showGestureFeedback(
        gestureTranslations.noSolvesToDelete || "No solves to delete"
      );
      return; // No solves to delete
    }

    // Get translations
    const translations = window.i18nModule?.translations || {};
    const timesTranslations = translations.times || {};
    const modalTranslations = translations.modals || {};

    // Show confirmation and delete the most recent solve (index 0)
    import("./modal-manager.js").then(({ showConfirm }) => {
      showConfirm(
        timesTranslations.confirmDelete ||
          "Are you sure you want to delete this time?",
        function () {
          times.splice(0, 1); // Remove the first (most recent) solve
          updateTimesList();
          updateStats();
          saveTimes();

          // Update timer display to show new most recent time (or 0.000 if no times left)
          if (timerState === TIMER_STATE.IDLE) {
            const newMostRecentTime = getPreviousTime();
            timerElement.textContent = formatTime(newMostRecentTime);
            updateDisplay(newMostRecentTime);
          }

          // Show brief feedback
          const gestureTranslations = translations.gestures || {};
          showGestureFeedback(
            gestureTranslations.solveDeleted || "Solve deleted"
          );
        },
        null,
        modalTranslations.confirm || "Confirm"
      );
    });
  }

  function handleSwipeUp() {
    // Add/cycle penalty to most recent solve
    const storageKey = currentSessionId
      ? `session_${currentSessionId}`
      : currentEvent;
    const times = timesMap[storageKey];

    if (!times || times.length === 0) {
      return; // No solves to add penalty to
    }

    const solve = times[0]; // Most recent solve

    // Skip MBLD and FMC solves for penalty cycling
    if (solve.isMBLD) {
      // Get translations
      const translations = window.i18nModule?.translations || {};
      const gestureTranslations = translations.gestures || {};
      showGestureFeedback(
        gestureTranslations.cannotAddPenaltyMBLD ||
          "Cannot add penalty to MBLD solve"
      );
      return;
    }

    if (solve.isFMC) {
      // For FMC, only toggle DNF
      if (solve.penalty === "DNF" || solve.isDNF) {
        // Remove DNF
        if (solve.originalMoveCount !== undefined) {
          solve.moveCount = solve.originalMoveCount;
          delete solve.originalMoveCount;
        }
        solve.penalty = null;
        solve.isDNF = false;
        solve.result = null;
        solve.time = solve.moveCount || 0;
        delete solve.dnfReason;
        // Get translations
        const translations = window.i18nModule?.translations || {};
        const gestureTranslations = translations.gestures || {};
        showGestureFeedback(gestureTranslations.dnfRemoved || "DNF removed");
      } else {
        // Add DNF
        if (typeof solve.moveCount === "number") {
          solve.originalMoveCount = solve.moveCount;
        }
        solve.penalty = "DNF";
        solve.isDNF = true;
        solve.result = "DNF";
        solve.time = Infinity;
        solve.dnfReason = "Added via gesture";
        // Get translations
        const translations = window.i18nModule?.translations || {};
        const gestureTranslations = translations.gestures || {};
        showGestureFeedback(gestureTranslations.dnfAdded || "DNF added");
      }
    } else {
      // Regular events - cycle through no penalty -> +2 -> DNF -> no penalty
      let rawTime = solve.time;

      // Get raw time without penalties
      if (solve.penalty === "+2") {
        rawTime = solve.time - 2000;
      } else if (
        solve.penalty === "DNF" &&
        solve.originalTime &&
        isFinite(solve.originalTime)
      ) {
        rawTime = solve.originalTime;
      }

      if (!solve.penalty) {
        // No penalty -> +2
        solve.time = rawTime + 2000;
        solve.penalty = "+2";
        solve.result = formatTime(solve.time) + " (+2)";
        delete solve.originalTime;
        // Get translations
        const translations = window.i18nModule?.translations || {};
        const gestureTranslations = translations.gestures || {};
        showGestureFeedback(
          gestureTranslations.plus2Added || "+2 penalty added"
        );
      } else if (solve.penalty === "+2") {
        // +2 -> DNF
        solve.originalTime = rawTime;
        solve.penalty = "DNF";
        solve.result = "DNF";
        solve.time = Infinity;
        // Get translations
        const translations = window.i18nModule?.translations || {};
        const gestureTranslations = translations.gestures || {};
        showGestureFeedback(gestureTranslations.dnfAdded || "DNF added");
      } else if (solve.penalty === "DNF") {
        // DNF -> no penalty
        solve.time = rawTime;
        solve.penalty = null;
        solve.result = null;
        delete solve.originalTime;
        // Get translations
        const translations = window.i18nModule?.translations || {};
        const gestureTranslations = translations.gestures || {};
        showGestureFeedback(
          gestureTranslations.penaltyRemoved || "Penalty removed"
        );
      }
    }

    updateTimesList();
    updateStats();
    saveTimes();
  }

  function handleSwipeLeftRight() {
    // Generate new scramble
    generateScramble();
    // Get translations
    const translations = window.i18nModule?.translations || {};
    const gestureTranslations = translations.gestures || {};
    showGestureFeedback(
      gestureTranslations.newScrambleGenerated || "New scramble generated"
    );
  }

  function showGestureFeedback(message) {
    // Create or update feedback element
    let feedback = document.getElementById("gesture-feedback");
    if (!feedback) {
      feedback = document.createElement("div");
      feedback.id = "gesture-feedback";
      feedback.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-size: 14px;
        z-index: 10000;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.2s ease;
      `;
      document.body.appendChild(feedback);
    }

    feedback.textContent = message;
    feedback.style.opacity = "1";

    // Hide after 1.5 seconds
    setTimeout(() => {
      feedback.style.opacity = "0";
    }, 1500);
  }

  function resetGestureState() {
    gestureInProgress = false;
    touchStartX = 0;
    touchStartY = 0;
    touchStartTime = 0;
  }

  // Get the most recent solve time for display
  function getPreviousTime() {
    const storageKey = currentSessionId
      ? `session_${currentSessionId}`
      : currentEvent;
    const times = timesMap[storageKey];

    if (!times || times.length === 0) {
      return 0; // Default to 0 if no previous times
    }

    const mostRecentSolve = times[0]; // Most recent solve is at index 0

    // Handle different solve types
    if (mostRecentSolve.isMBLD) {
      // For MBLD, return the time component
      return mostRecentSolve.time || 0;
    } else if (mostRecentSolve.isFMC) {
      // For FMC, return the elapsed time or 0
      return mostRecentSolve.elapsedTime || 0;
    } else {
      // For regular solves, return the time
      return mostRecentSolve.time || 0;
    }
  }

  // Touch events with gesture detection
  container.addEventListener("touchstart", function (event) {
    // Don't handle gestures if any modal is open
    const openModals = document.querySelectorAll(
      '[class*="modal"].show, .stats-details-panel.show'
    );
    if (openModals.length > 0) {
      return;
    }

    if (!shouldHandleInput(event)) {
      return;
    }

    const touch = event.touches[0];
    touchStartX = touch.clientX;
    touchStartY = touch.clientY;
    touchStartTime = Date.now();
    gestureInProgress = false;

    // If timer is in inspection or running state, start timer input immediately
    // and disable gesture detection
    if (
      timerState === TIMER_STATE.INSPECTION ||
      timerState === TIMER_STATE.RUNNING
    ) {
      event.preventDefault();
      handleTimerInputStart();
      gestureInProgress = true; // Prevent gesture detection
      return;
    }

    // For idle state, wait for touchend to determine if it's a gesture or timer input
  });

  container.addEventListener("touchend", function (event) {
    // Don't handle gestures if any modal is open
    const openModals = document.querySelectorAll(
      '[class*="modal"].show, .stats-details-panel.show'
    );
    if (openModals.length > 0) {
      return;
    }

    if (!shouldHandleInput(event)) {
      return;
    }

    // If timer input was started in touchstart (during inspection/running),
    // just handle the timer input end
    if (
      timerState === TIMER_STATE.HOLDING ||
      timerState === TIMER_STATE.READY ||
      timerState === TIMER_STATE.RUNNING
    ) {
      event.preventDefault();
      handleTimerInputEnd();
      return;
    }

    // If a gesture is already in progress, don't handle as normal touch
    if (gestureInProgress) {
      event.preventDefault();
      return;
    }

    const touch = event.changedTouches[0];
    const touchEndX = touch.clientX;
    const touchEndY = touch.clientY;
    const touchEndTime = Date.now();

    const deltaX = touchEndX - touchStartX;
    const deltaY = touchEndY - touchStartY;
    const deltaTime = touchEndTime - touchStartTime;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const velocity = distance / deltaTime;

    // Only allow gestures when timer is in IDLE state
    // Disable gestures during inspection, holding, ready, or running states
    const gesturesAllowed = timerState === TIMER_STATE.IDLE;

    // Check for swipe gestures (only if gestures are allowed)
    if (
      gesturesAllowed &&
      distance > SWIPE_THRESHOLD &&
      velocity > SWIPE_VELOCITY_THRESHOLD &&
      deltaTime < GESTURE_TIMEOUT
    ) {
      event.preventDefault();
      gestureInProgress = true;

      // Determine swipe direction
      const absDeltaX = Math.abs(deltaX);
      const absDeltaY = Math.abs(deltaY);

      if (absDeltaY > absDeltaX) {
        // Vertical swipe
        if (deltaY < 0) {
          // Swipe up
          handleSwipeUp();
        } else {
          // Swipe down
          handleSwipeDown();
        }
      } else {
        // Horizontal swipe (left or right)
        handleSwipeLeftRight();
      }

      return;
    }

    // If we get here, it's a regular tap (not a swipe)
    // Handle normal timer controls only when gestures are allowed (timer is idle)
    if (!gestureInProgress && gesturesAllowed) {
      event.preventDefault();

      // Special handling for FMC mode
      if (currentEvent === "333fm") {
        if (!isFMCInProgress()) {
          // Start FMC attempt
          startFMCAttempt();
          return;
        } else {
          // If FMC is in progress, prevent touch from triggering the timer
          return;
        }
      }

      // For normal timer operation, we need to simulate the full touch cycle
      // since we didn't start timer input in touchstart (to avoid conflicts with gestures)

      // Calculate touch duration to determine if it should be treated as a hold or tap
      const touchDuration = Date.now() - touchStartTime;

      if (timerState === TIMER_STATE.RUNNING) {
        // If timer is running, stop it
        handleTimerInputEnd();
      } else if (touchDuration < 150) {
        // Short tap - simulate quick press and release for inspection or immediate start
        handleTimerInputStart();
        // Small delay to simulate the hold, then release
        setTimeout(() => {
          handleTimerInputEnd();
        }, 10);
      } else {
        // Longer touch - treat as hold and release
        handleTimerInputStart();
        handleTimerInputEnd();
      }
    }
  });

  // Handle touch cancel (when touch is interrupted)
  container.addEventListener("touchcancel", function (event) {
    resetGestureState();
    // Only call handleTimerInputEnd if timer input was actually started
    // Since we don't start timer input in touchstart anymore, we might not need this
    // But it's safe to call in case the timer state needs resetting
    if (timerState !== TIMER_STATE.IDLE) {
      handleTimerInputEnd();
    }
  });

  // Prevent pull-to-refresh on mobile devices (fallback for older browsers)
  let lastTouchY = 0;
  let preventPullToRefresh = false;

  // Function to check if element is scrollable and at top
  function isElementScrollableAndAtTop(element) {
    if (!element) return false;

    // Check if element has scrollable content
    const hasScrollableContent = element.scrollHeight > element.clientHeight;
    if (!hasScrollableContent) return false;

    // Check if element is at the top
    return element.scrollTop === 0;
  }

  // Function to find the closest scrollable parent
  function findScrollableParent(element) {
    if (!element || element === document.body) return null;

    const computedStyle = window.getComputedStyle(element);
    const overflowY = computedStyle.overflowY;

    if (overflowY === "auto" || overflowY === "scroll") {
      return element;
    }

    return findScrollableParent(element.parentElement);
  }

  document.addEventListener(
    "touchstart",
    function (e) {
      if (e.touches.length !== 1) return;
      lastTouchY = e.touches[0].clientY;

      const target = e.target;

      // Check if touch is within a scrollable container
      const scrollableParent = findScrollableParent(target);

      // Check if touch is within modal, dropdown, or times panel
      const isInModal = target.closest(
        ".modal, .custom-modal, .settings-modal, .solve-details-modal, .mbld-modal, .fmc-modal, .fmc-result-modal, .stats-details-panel"
      );
      const isInDropdown = target.closest(
        ".event-dropdown, .stats-event-dropdown"
      );
      const isInTimesPanel = target.closest(".times-panel");
      const isInScrollableArea = target.closest(
        ".times-list, .solve-details-body, .settings-body, .stats-details-content"
      );

      // Only prevent pull-to-refresh if:
      // 1. We're at the top of the main page (not in any scrollable container)
      // 2. Not touching any modal, dropdown, or scrollable area
      // 3. Not touching the times panel
      if (scrollableParent) {
        // If there's a scrollable parent, don't prevent pull-to-refresh
        preventPullToRefresh = false;
      } else if (
        isInModal ||
        isInDropdown ||
        isInTimesPanel ||
        isInScrollableArea
      ) {
        // If touching modal, dropdown, times panel, or scrollable area, don't prevent
        preventPullToRefresh = false;
      } else {
        // Only prevent if we're at the top of the main page and not in any special area
        preventPullToRefresh = window.pageYOffset === 0;
      }
    },
    { passive: false }
  );

  document.addEventListener(
    "touchmove",
    function (e) {
      const touchY = e.touches[0].clientY;
      const touchYDelta = touchY - lastTouchY;
      lastTouchY = touchY;

      if (preventPullToRefresh) {
        const target = e.target;

        // Double-check: don't prevent if we're in a scrollable area
        const isInScrollableArea = target.closest(
          ".times-list, .solve-details-body, .settings-body, .stats-details-content, .modal, .custom-modal, .settings-modal, .solve-details-modal, .mbld-modal, .fmc-modal, .fmc-result-modal, .stats-details-panel, .event-dropdown, .stats-event-dropdown, .times-panel"
        );

        if (!isInScrollableArea && touchYDelta > 0) {
          // Only prevent pull-to-refresh if scrolling down from the top and not in scrollable areas
          e.preventDefault();
          return;
        }
      }
    },
    { passive: false }
  );

  document.addEventListener(
    "touchend",
    function (e) {
      preventPullToRefresh = false;
    },
    { passive: false }
  );

  // Reset gesture state when modals open or other interruptions occur
  document.addEventListener("keydown", function (event) {
    if (event.key === "Escape") {
      resetGestureState();

      // If inspection is active, trigger the times toggle to cancel inspection
      if (
        timerState === TIMER_STATE.INSPECTION ||
        timerState === TIMER_STATE.HOLDING ||
        timerState === TIMER_STATE.READY
      ) {
        // Trigger the times toggle click to cancel inspection
        timesToggle.click();
      }
    }

    // Handle CTRL+Z to delete most recent solve
    if (event.ctrlKey && event.key === "z") {
      // Don't handle if a modal is open
      if (
        document.querySelector(
          ".modal.show, .custom-modal.show, .settings-modal.show, .solve-details-modal.show, .mbld-modal.show, .fmc-modal.show, .fmc-result-modal.show"
        )
      ) {
        return;
      }

      // Don't handle if focus is in text inputs or textareas
      if (
        document.activeElement.tagName === "TEXTAREA" ||
        document.activeElement.tagName === "INPUT"
      ) {
        return;
      }

      // Don't handle during timer states other than idle
      if (timerState !== TIMER_STATE.IDLE) {
        return;
      }

      event.preventDefault();

      // Delete most recent solve (same logic as swipe down gesture)
      const storageKey = currentSessionId
        ? `session_${currentSessionId}`
        : currentEvent;
      const times = timesMap[storageKey];

      if (!times || times.length === 0) {
        return; // No solves to delete
      }

      // Get translations
      const translations = window.i18nModule?.translations || {};
      const timesTranslations = translations.times || {};
      const modalTranslations = translations.modals || {};

      // Show confirmation and delete the most recent solve (index 0)
      import("./modal-manager.js").then(({ showConfirm }) => {
        showConfirm(
          timesTranslations.confirmDelete ||
            "Are you sure you want to delete this time?",
          function () {
            times.splice(0, 1); // Remove the first (most recent) solve
            updateTimesList();
            updateStats();
            saveTimes();

            // Update timer display to show new most recent time (or 0.000 if no times left)
            if (timerState === TIMER_STATE.IDLE) {
              const newMostRecentTime = getPreviousTime();
              timerElement.textContent = formatTime(newMostRecentTime);
              updateDisplay(newMostRecentTime);
            }
          },
          null,
          modalTranslations.confirm || "Confirm"
        );
      });
    }
  });

  // Mouse events for desktop click support
  container.addEventListener("mousedown", function (event) {
    // Special handling for FMC mode
    if (
      currentEvent === "333fm" &&
      event.button === 0 &&
      shouldHandleInput(event)
    ) {
      // If FMC is not in progress, start a new attempt
      if (!isFMCInProgress()) {
        event.preventDefault();
        // Start FMC attempt
        startFMCAttempt();
        return;
      } else {
        // If FMC is in progress, prevent mouse from triggering the timer
        event.preventDefault();
        return;
      }
    }

    // Only handle left mouse button
    if (event.button === 0 && shouldHandleInput(event)) {
      event.preventDefault();
      handleTimerInputStart();
    }
  });

  container.addEventListener("mouseup", function (event) {
    // Only handle left mouse button
    if (event.button === 0 && shouldHandleInput(event)) {
      event.preventDefault();
      handleTimerInputEnd();
    }
  });

  // Settings modal handlers
  settingsButton.addEventListener("click", function () {
    settingsModal.classList.add("show");
  });

  settingsClose.addEventListener("click", function () {
    settingsModal.classList.remove("show");
  });

  settingsSave.addEventListener("click", function () {
    settingsModal.classList.remove("show");
    // Settings are automatically saved as they're changed via checkboxes
    // This just closes the modal
  });

  // Times panel toggle handler function
  function timesToggleHandler() {
    // Special handling during inspection - cancel inspection and reset
    if (
      timerState === TIMER_STATE.INSPECTION ||
      timerState === TIMER_STATE.HOLDING ||
      timerState === TIMER_STATE.READY
    ) {
      // Get previous time before resetting
      const previousTime = getPreviousTime();

      // Cancel inspection and reset timer completely
      if (inspectionInterval) {
        clearInterval(inspectionInterval);
        inspectionInterval = null;
      }

      // Cancel scheduled sound timeouts
      if (sound8SecTimeout) {
        clearTimeout(sound8SecTimeout);
        sound8SecTimeout = null;
      }
      if (sound12SecTimeout) {
        clearTimeout(sound12SecTimeout);
        sound12SecTimeout = null;
      }

      // Clear any held intervals
      clearInterval(spaceHeldInterval);
      spaceHeldTimeElement.textContent = "0";

      // Reset timer state and intervals
      clearInterval(timerInterval);
      elapsedTime = 0;

      // Reset inspection variables to ensure display works correctly
      inspectionTimeLeft = INSPECTION_TIME;
      inspectionPenalty = null;

      // Make sure timing-active and inspection-active classes are removed
      document.body.classList.remove("timing-active");
      document.body.classList.remove("inspection-active");

      // Update timer state to idle BEFORE updating display
      updateTimerState(TIMER_STATE.IDLE);

      // Immediately force the timer display to show the correct time
      // Bypass the updateDisplay function to avoid any inspection state conflicts
      timerElement.textContent = formatTime(previousTime);

      // Remove any inspection-related CSS classes from timer element
      timerElement.classList.remove("inspection", "ready", "running");

      // Also call updateDisplay as backup to ensure consistency
      updateDisplay(previousTime);

      // Restore times panel to its previous state when inspection is canceled
      if (timesPanelWasOpenBeforeInspection) {
        // Panel was open before inspection - reopen it
        timesPanel.classList.add("show");

        // Add class to body for large screen layout
        if (window.innerWidth >= 1024) {
          document.body.classList.add("panel-open");
          const container = document.querySelector(".container");
          container.style.marginLeft = "350px";
          container.style.width = "calc(100% - 350px)";
          container.style.transform = "scale(0.9)";
          container.style.transformOrigin = "center center";
        }

        // Update toggle button to show close icon
        timesToggle.classList.add("active");
        const icon = timesToggle.querySelector("i");
        if (icon) {
          icon.classList.remove("fa-history");
          icon.classList.add("fa-times");
        }

        // Update toggle position
        updateTimesTogglePosition();
      } else {
        // Panel was closed before inspection - keep it closed
        // Reset times toggle icon to history
        const icon = timesToggle.querySelector("i");
        if (icon) {
          icon.classList.remove("fa-times");
          icon.classList.add("fa-history");
        }
        timesToggle.classList.remove("active");
      }

      // Reset the state tracking variable since inspection was canceled
      timesPanelWasOpenBeforeInspection = false;

      // Don't generate new scramble - just reset
      return;
    }

    // Normal toggle behavior for idle state
    const isVisible = timesPanel.classList.contains("show");

    if (isVisible) {
      // Close the panel
      closeTimesPanel();
    } else {
      // Open the panel
      timesPanel.classList.add("show");

      // Add class to body for large screen layout
      if (window.innerWidth >= 1024) {
        document.body.classList.add("panel-open");
        const container = document.querySelector(".container");
        container.style.marginLeft = "350px";
        container.style.width = "calc(100% - 350px)";
        container.style.transform = "scale(0.9)";
        container.style.transformOrigin = "center center";
      }

      // Update toggle button
      timesToggle.classList.add("active");
      const icon = timesToggle.querySelector("i");
      icon.classList.remove("fa-history");
      icon.classList.add("fa-times");

      // Always update the position to ensure it's correct
      updateTimesTogglePosition();
    }

    // Save the times list state after toggling
    saveTimesListState();
  }

  // Add the event listener for times toggle
  timesToggle.addEventListener("click", timesToggleHandler);

  // Mobile close button handler
  if (timesPanelClose) {
    timesPanelClose.addEventListener("click", function () {
      // Close the panel
      closeTimesPanel();
    });
  }

  // Handle window resize for panel
  window.addEventListener("resize", function () {
    if (timesPanel.classList.contains("show")) {
      const container = document.querySelector(".container");
      if (window.innerWidth >= 1024) {
        document.body.classList.add("panel-open");
        container.style.marginLeft = "350px";
        container.style.width = "calc(100% - 350px)";
        container.style.transform = "scale(0.9)";

        // Ensure icon is correct
        const icon = timesToggle.querySelector("i");
        if (!icon.classList.contains("fa-times")) {
          icon.classList.remove("fa-history");
          icon.classList.add("fa-times");
        }
      } else {
        document.body.classList.remove("panel-open");
        container.style.marginLeft = "0";
        container.style.width = "100%";
        container.style.transform = "scale(1)";

        // Always update the position to ensure it's correct
        updateTimesTogglePosition();
      }
    }
  });

  // Close modals when clicking outside
  document.addEventListener("click", function (event) {
    // Close settings modal when clicking outside
    if (
      settingsModal.classList.contains("show") &&
      !event.target.closest(".settings-content") &&
      !event.target.closest("#settings-btn")
    ) {
      settingsModal.classList.remove("show");
    }
  });

  // New scramble button
  newScrambleButton.addEventListener("click", function () {
    // Check if we're already generating a scramble
    if (window.isGeneratingScramble) {
      return;
    }

    // For MBLD, generate new scrambles with the same cube count
    if (currentEvent === "333mbf") {
      generateMBLDScrambles(getMBLDCubeCount());
    } else if (currentEvent === "333fm" && isFMCInProgress()) {
      restartFMCWithNewScramble();
    } else {
      // Handle scramble generation based on event type
      if (currentEvent === "333fm") {
        // For FMC, use the FMC module to generate a new scramble

        // Show loader while generating
        const scrambleLoader = document.getElementById("scramble-loader");
        const scrambleText = document.getElementById("scramble-text");

        if (scrambleLoader) {
          scrambleLoader.style.display = "inline-block";
        }

        if (scrambleText) {
          scrambleText.classList.add("hidden");
        }

        // Hide scramble initially
        const scrambleElement = document.getElementById("scramble");
        if (scrambleElement) {
          scrambleElement.style.visibility = "hidden";
        }

        // Import FMC manager and generate FMC scramble
        import("./fmc-manager.js")
          .then(({ generateFMCScramble, setupFMCInterface }) => {
            // Generate FMC scramble
            return generateFMCScramble().then((scramble) => {
              // Update current scramble
              currentScramble = scramble;

              // Update scramble text but keep it hidden
              if (scrambleText) {
                scrambleText.textContent = scramble;
                scrambleText.classList.add("hidden");
              }

              // Hide loader
              if (scrambleLoader) {
                scrambleLoader.style.display = "none";
              }

              // Setup FMC interface
              setupFMCInterface();
            });
          })
          .catch((error) => {
            // Error loading FMC manager for new scramble

            // Hide loader
            if (scrambleLoader) {
              scrambleLoader.style.display = "none";
            }

            // Show error message
            if (scrambleText) {
              scrambleText.textContent = "Error loading FMC module";
              scrambleText.classList.remove("hidden");
            }
          });
      } else {
        // Generating regular scramble for non-FMC event

        // For non-FMC events, force scramble element to be visible
        const scrambleElement = document.getElementById("scramble");
        if (scrambleElement) {
          scrambleElement.style.visibility = "visible";
        }

        // Clear the scramble text first
        const scrambleText = document.getElementById("scramble-text");
        if (scrambleText) {
          scrambleText.textContent = "";
          scrambleText.classList.add("hidden");
        }

        // Reset current scramble
        currentScramble = "";

        // Generate new scramble
        generateScramble()
          .then((scramble) => {
            // Scramble generated

            // Ensure the scramble text is updated
            if (scrambleText) {
              scrambleText.textContent = currentScramble;
              scrambleText.classList.remove("hidden");
            }
          })
          .catch((error) => {
            // Error generating scramble
          });
      }
    }
  });

  // Clear times button
  clearTimesButton.addEventListener("click", function () {
    // Get translations
    const translations = window.i18nModule?.translations || {};
    const timesTranslations = translations.times || {};
    const modalTranslations = translations.modals || {};
    const buttonTranslations = translations.buttons || {};

    import("./modal-manager.js").then(({ showConfirm }) => {
      showConfirm(
        timesTranslations.confirmClear ||
          "Are you sure you want to clear all times for this event?",
        function () {
          // Determine the storage key based on whether we're in a custom session or not
          const storageKey = currentSessionId
            ? `session_${currentSessionId}`
            : currentEvent;

          timesMap[storageKey] = [];
          updateTimesList();
          updateStats();
          saveTimes();

          // Update timer display to show 0.000 since all times are cleared
          if (timerState === TIMER_STATE.IDLE) {
            timerElement.textContent = formatTime(0);
            updateDisplay(0);
          }
        },
        null,
        modalTranslations.confirm || "Confirm"
      );
    });
  });

  // Edit Session Modal Functions
  const editSessionModal = document.getElementById("edit-session-modal");
  const editSessionClose = document.getElementById("edit-session-close");
  const editSessionSave = document.getElementById("edit-session-save");
  const editSessionDelete = document.getElementById("edit-session-delete");
  const editSessionNameInput = document.getElementById("edit-session-name");
  const editSessionPuzzleSelect = document.getElementById(
    "edit-session-puzzle"
  );
  const editSessionIdInput = document.getElementById("edit-session-id");

  // Function to show the edit session modal
  function showEditSessionModal() {
    if (!editSessionModal) return;

    // Find the current session
    const currentSession = customSessions.find(
      (session) =>
        session.puzzleType === currentEvent &&
        session.name === currentEventText.textContent
    );

    if (!currentSession) {
      // Could not find current session to edit
      return;
    }

    // Populate form fields
    if (editSessionNameInput) {
      editSessionNameInput.value = currentSession.name;
    }

    if (editSessionPuzzleSelect) {
      editSessionPuzzleSelect.value = currentSession.puzzleType;
    }

    if (editSessionIdInput) {
      editSessionIdInput.value = currentSession.id;
    }

    // Show the modal
    editSessionModal.classList.add("show");

    // Focus the session name input
    if (editSessionNameInput) {
      setTimeout(() => {
        editSessionNameInput.focus();
        editSessionNameInput.select();
      }, 100);
    }
  }

  // Function to hide the edit session modal
  function hideEditSessionModal() {
    if (!editSessionModal) return;
    editSessionModal.classList.remove("show");
  }

  // Function to save edited session
  function saveEditedSession() {
    const sessionName = editSessionNameInput.value.trim() || "My Session";
    const puzzleType = editSessionPuzzleSelect.value;
    const sessionId = editSessionIdInput.value;

    // Get the puzzle name from the selected option
    const puzzleName =
      editSessionPuzzleSelect.options[editSessionPuzzleSelect.selectedIndex]
        .textContent;

    // Saving edited session

    // Find the session index
    const sessionIndex = customSessions.findIndex(
      (session) => session.id === sessionId
    );

    if (sessionIndex === -1) {
      // Could not find session to edit
      return;
    }

    // Update the session object
    customSessions[sessionIndex].name = sessionName;
    customSessions[sessionIndex].puzzleType = puzzleType;

    // Save to localStorage
    saveCustomSessions();

    // Update the dropdown
    updateCustomSessionsInDropdown();

    // Update button text and icon
    currentEventText.textContent = sessionName;

    // Set the appropriate icon class
    // For non-WCA puzzles, use the unofficial- prefix for icons only
    const nonWcaPuzzleIcons = {
      fto: "unofficial-fto",
      master_tetraminx: "unofficial-mtetram",
      kilominx: "unofficial-kilominx",
      redi_cube: "unofficial-redi",
      baby_fto: "unofficial-baby_fto",
    };

    if (nonWcaPuzzleIcons[puzzleType] !== undefined) {
      // For non-WCA puzzles with icons, use the unofficial- prefix
      if (nonWcaPuzzleIcons[puzzleType]) {
        currentEventIcon.className = `cubing-icon ${nonWcaPuzzleIcons[puzzleType]}`;
      } else {
        // For puzzles without icons, just use a generic icon or none
        currentEventIcon.className = ""; // No icon
      }
    } else {
      // For WCA events, use the event- prefix
      currentEventIcon.className = `cubing-icon event-${puzzleType}`;
    }

    // Update current event
    currentEvent = puzzleType;
    currentEventElement.textContent = puzzleType;

    // Update FMC keyboard label state
    updateFMCKeyboardLabelState();

    // Process the event change
    processEventChange(puzzleType, sessionName);

    // Hide the modal
    hideEditSessionModal();

    // Show a confirmation message
    import("./modal-manager.js").then(({ showAlert }) => {
      showAlert(
        `Session "${sessionName}" has been updated with puzzle type: ${puzzleName}`
      );
    });
  }

  // Function to delete session
  function deleteSession() {
    // Get translations
    const translations = window.i18nModule?.translations || {};
    const sessionsTranslations = translations.sessions || {};
    const modalTranslations = translations.modals || {};

    const sessionId = editSessionIdInput.value;
    const sessionIndex = customSessions.findIndex(
      (session) => session.id === sessionId
    );

    if (sessionIndex === -1) {
      // Could not find session to delete
      return;
    }

    // Store session name before hiding the modal
    const sessionName = customSessions[sessionIndex].name;

    // Hide the edit session modal first to prevent modal stacking issues
    hideEditSessionModal();

    // Small delay to ensure the edit modal is fully hidden
    setTimeout(() => {
      import("./modal-manager.js").then(({ showConfirm }) => {
        showConfirm(
          sessionsTranslations.confirmDelete ||
            `Are you sure you want to delete the session "${sessionName}"?`,
          function () {
            // Remove the session from the array
            const deletedSession = customSessions.splice(sessionIndex, 1)[0];

            // Remove the session's times from timesMap
            const sessionKey = `session_${deletedSession.id}`;
            if (timesMap[sessionKey]) {
              delete timesMap[sessionKey];
              saveTimes(); // Save the updated times
            }

            // Save the updated sessions
            saveCustomSessions();

            // Update the dropdown
            updateCustomSessionsInDropdown();

            // Always switch to default 3x3 event
            const eventId = "333";
            const eventText = "3×3×3";

            // Update button text and icon
            currentEventText.textContent = eventText;

            // Set the appropriate icon class
            // For non-WCA puzzles, use the unofficial- prefix for icons only
            const nonWcaPuzzleIcons = {
              fto: "unofficial-fto",
              master_tetraminx: "unofficial-mtetram",
              kilominx: "unofficial-kilominx",
              redi_cube: "unofficial-redi",
              baby_fto: "unofficial-baby_fto",
            };

            if (nonWcaPuzzleIcons[eventId] !== undefined) {
              // For non-WCA puzzles with icons, use the unofficial- prefix
              if (nonWcaPuzzleIcons[eventId]) {
                currentEventIcon.className = `cubing-icon ${nonWcaPuzzleIcons[eventId]}`;
              } else {
                // For puzzles without icons, just use a generic icon or none
                currentEventIcon.className = ""; // No icon
              }
            } else {
              // For WCA events, use the event- prefix
              currentEventIcon.className = `cubing-icon event-${eventId}`;
            }

            // Update current event and reset session ID
            currentEvent = eventId;
            currentEventElement.textContent = eventId;
            currentSessionId = null;

            // Update FMC keyboard label state
            updateFMCKeyboardLabelState();

            // Process the event change
            processEventChange(eventId, eventText);

            // Show confirmation message
            import("./modal-manager.js").then(({ showAlert }) => {
              showAlert(`Session "${deletedSession.name}" has been deleted.`);
            });
          },
          null,
          modalTranslations.confirm || "Confirm"
        );
      });
    }, 100); // Small delay to ensure smooth transition
  }

  // Event listeners for the edit session modal
  if (editSessionClose) {
    editSessionClose.addEventListener("click", hideEditSessionModal);
  }

  if (editSessionSave) {
    editSessionSave.addEventListener("click", saveEditedSession);
  }

  if (editSessionDelete) {
    editSessionDelete.addEventListener("click", deleteSession);
  }

  // Close modal when clicking outside
  if (editSessionModal) {
    editSessionModal.addEventListener("click", function (event) {
      if (event.target === editSessionModal) {
        hideEditSessionModal();
      }
    });
  }

  // Allow Enter key to submit the form
  if (editSessionNameInput) {
    editSessionNameInput.addEventListener("keypress", function (event) {
      if (event.key === "Enter") {
        saveEditedSession();
      }
    });
  }

  // Edit session button
  const editSessionButton = document.getElementById("edit-session-btn");
  if (editSessionButton) {
    editSessionButton.addEventListener("click", function () {
      showEditSessionModal();
    });
  }

  // Dark mode toggle
  darkModeCheckbox.addEventListener("change", function () {
    if (this.checked) {
      document.body.classList.add("dark-mode");
    } else {
      document.body.classList.remove("dark-mode");
    }
    localStorage.setItem("scTimer-darkMode", this.checked);
  });

  // FMC keyboard toggle
  if (showFMCKeyboardCheckbox) {
    showFMCKeyboardCheckbox.addEventListener("change", function () {
      localStorage.setItem("scTimer-showFMCKeyboard", this.checked);

      // If we're currently in FMC mode, update the keyboard visibility
      if (currentEvent === "333fm") {
        // If we're in an FMC attempt, update the keyboard visibility
        if (isFMCInProgress()) {
          updateFMCKeyboardVisibility(this.checked);
        }
      }
    });
  }

  // Inspection checkbox toggle
  if (useInspectionCheckbox) {
    useInspectionCheckbox.addEventListener("change", function () {
      // Save the inspection setting
      localStorage.setItem("scTimer-inspection", this.checked);

      // Enable/disable inspection sound dropdown based on inspection checkbox
      if (inspectionSoundSelector) {
        inspectionSoundSelector.disabled = !this.checked;
        if (inspectionSoundSelector.parentElement) {
          if (this.checked) {
            inspectionSoundSelector.parentElement.classList.remove("disabled");
          } else {
            inspectionSoundSelector.parentElement.classList.add("disabled");
          }
        }
      }
    });
  }

  // Inspection sound dropdown change
  if (inspectionSoundSelector) {
    inspectionSoundSelector.addEventListener("change", function () {
      localStorage.setItem("scTimer-inspectionSound", this.value);
    });
  }

  // Timer mode selector
  if (timerModeSelector) {
    timerModeSelector.addEventListener("change", function () {
      const mode = this.value;
      localStorage.setItem("scTimer-timerMode", mode);

      // Toggle manual input timer based on mode
      toggleManualInputTimer(mode === "typing");

      // Toggle Stackmat reset inspection setting visibility
      toggleStackmatResetInspectionSetting(mode === "stackmat");

      // Toggle microphone selector visibility and populate options
      toggleMicrophoneSelectorVisibility(mode === "stackmat");

      // Handle Stackmat mode
      handleStackmatModeChange(mode === "stackmat");

      // Handle Bluetooth mode (currently disabled)
      if (mode === "bluetooth") {
        // Revert to timer mode since Bluetooth is not implemented yet
        this.value = "timer";
        localStorage.setItem("scTimer-timerMode", "timer");
      }
    });
  }

  // Microphone selector
  if (microphoneSelector) {
    microphoneSelector.addEventListener("change", function () {
      const selectedDeviceId = this.value;
      localStorage.setItem("scTimer-selectedMicrophone", selectedDeviceId);

      // If Stackmat is currently active, restart it with the new microphone
      if (isStackmatMode && stackmatManager) {
        handleStackmatModeChange(false); // Stop current
        setTimeout(() => {
          handleStackmatModeChange(true); // Restart with new microphone
        }, 100);
      }
    });
  }

  // Initialize Stackmat manager
  async function initStackmatManager() {
    try {
      if (typeof StackmatManager === "undefined") {
        return false;
      }

      stackmatManager = new StackmatManager();
      const initialized = await stackmatManager.init();

      if (initialized) {
        setupStackmatCallbacks();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      return false;
    }
  }

  // Setup Stackmat callbacks
  function setupStackmatCallbacks() {
    if (!stackmatManager) return;

    // Time update callback
    stackmatManager.setTimeUpdateCallback((timeMs, timeString) => {
      if (isStackmatMode && timerState !== TIMER_STATE.INSPECTION) {
        // Only update timer display if not in inspection mode
        timerElement.textContent = formatTime(timeMs);
      }
    });

    // State change callback
    stackmatManager.setStateChangeCallback((state, finalTime) => {
      updateStackmatStatus(state);

      if (state === "stopped" && isStackmatMode) {
        // Only save if we have a valid time
        if (finalTime && finalTime > 0) {
          // Timer stopped - save the time automatically
          saveTime(finalTime);

          // Update the UI
          updateTimesList();
          updateStats();
          saveTimes();

          // Generate new scramble
          generateScramble();
        }
      }
    });

    // Timer start callback
    stackmatManager.setTimerStartCallback(() => {
      if (isStackmatMode) {
        // If inspection is running, stop it first
        if (timerState === TIMER_STATE.INSPECTION) {
          stopInspection();
        }

        timerState = TIMER_STATE.RUNNING;

        // Add timing-active class to hide UI elements
        document.body.classList.add("timing-active");

        // Clear ready and inspection states, add running state
        timerElement.classList.remove("ready", "inspection");
        timerElement.classList.add("running");
      }
    });

    // Timer stop callback
    stackmatManager.setTimerStopCallback((finalTime) => {
      if (isStackmatMode) {
        timerState = TIMER_STATE.IDLE;

        // Remove timing-active class to show UI elements again
        document.body.classList.remove("timing-active");

        // Clear all timer state classes
        timerElement.classList.remove("running", "ready", "inspection");

        // Restore times panel state if it was open before inspection (same as normal timer)
        if (timesPanelWasOpenBeforeInspection) {
          // Reopen the panel
          timesPanel.classList.add("show");

          // Add class to body for large screen layout
          if (window.innerWidth >= 1024) {
            document.body.classList.add("panel-open");
            const container = document.querySelector(".container");
            container.style.marginLeft = "350px";
            container.style.width = "calc(100% - 350px)";
            container.style.transform = "scale(0.9)";
            container.style.transformOrigin = "center center";
          }

          // Update toggle button to show close icon
          timesToggle.classList.add("active");
          const icon = timesToggle.querySelector("i");
          if (icon) {
            icon.classList.remove("fa-history");
            icon.classList.add("fa-times");
          }

          // Update toggle position
          updateTimesTogglePosition();
        } else {
          // Reset times toggle to history icon if panel was closed
          const icon = timesToggle.querySelector("i");
          if (icon) {
            icon.classList.remove("fa-times");
            icon.classList.add("fa-history");
          }
          timesToggle.classList.remove("active");
        }

        // Reset the state tracking variable
        timesPanelWasOpenBeforeInspection = false;
      }
    });

    // Time reset to 0.000 callback for inspection
    stackmatManager.setLeftHandTapCallback(() => {
      if (isStackmatMode && timerState === TIMER_STATE.IDLE) {
        // Check if Stackmat reset inspection is enabled
        if (
          stackmatResetInspectionCheckbox &&
          stackmatResetInspectionCheckbox.checked
        ) {
          // Start inspection if both reset setting and inspection are enabled
          if (useInspectionCheckbox && useInspectionCheckbox.checked) {
            startInspection();
          }
        }
      }
    });

    // Both hands callback for ready state
    stackmatManager.setBothHandsChangeCallback((bothHandsDown) => {
      if (
        isStackmatMode &&
        (timerState === TIMER_STATE.IDLE ||
          timerState === TIMER_STATE.INSPECTION)
      ) {
        if (bothHandsDown) {
          // Both hands down - show ready state (green)
          timerElement.classList.add("ready");
          if (timerState === TIMER_STATE.INSPECTION) {
            // If in inspection, add ready class to inspection display
            timerElement.classList.add("inspection", "ready");
          }
        } else {
          // Hands lifted - remove ready state
          timerElement.classList.remove("ready");
          if (timerState === TIMER_STATE.INSPECTION) {
            // Keep inspection class but remove ready
            timerElement.classList.remove("ready");
          }
        }
      }
    });
  }

  // Handle Stackmat mode change
  async function handleStackmatModeChange(enableStackmat) {
    isStackmatMode = enableStackmat;

    if (enableStackmat) {
      // Initialize Stackmat if not already done
      if (!stackmatManager) {
        const initialized = await initStackmatManager();
        if (!initialized) {
          // Failed to initialize, revert to timer mode
          timerModeSelector.value = "timer";
          localStorage.setItem("scTimer-timerMode", "timer");
          isStackmatMode = false;
          return;
        }
      }

      // Start Stackmat capture
      try {
        // Get selected microphone device ID
        const selectedMicrophone = localStorage.getItem(
          "scTimer-selectedMicrophone"
        );
        let deviceId = null;

        if (selectedMicrophone && selectedMicrophone !== "auto") {
          // Use specific device
          deviceId = selectedMicrophone;
        } else {
          // Use auto-detection
        }

        await stackmatManager.startCapture(deviceId);
        showStackmatStatus(true);
      } catch (error) {
        // Show user-friendly error message
        import("./modal-manager.js").then(({ showAlert }) => {
          showAlert(
            error.message ||
              "Failed to start Stackmat timer. Please check your microphone connection and permissions.",
            "Stackmat Error"
          );
        });

        // Revert to timer mode
        timerModeSelector.value = "timer";
        localStorage.setItem("scTimer-timerMode", "timer");
        isStackmatMode = false;
        showStackmatStatus(false);
      }
    } else {
      // Stop Stackmat capture
      if (stackmatManager) {
        stackmatManager.stopCapture();
      }
      showStackmatStatus(false);
    }
  }

  // Update Stackmat status display
  function updateStackmatStatus(state) {
    if (!stackmatConnectionText) return;

    // Get translations
    const translations = window.i18nModule?.translations || {};
    const stackmatTranslations = translations.stackmat || {};

    const connection = document.getElementById("stackmat-connection");

    switch (state) {
      case "connected":
        stackmatConnectionText.textContent =
          stackmatTranslations.connected || "Connected";
        if (connection) {
          connection.classList.remove("disconnected");
          connection.classList.add("connected");
        }
        break;
      case "disconnected":
        stackmatConnectionText.textContent =
          stackmatTranslations.disconnected || "Disconnected";
        if (connection) {
          connection.classList.remove("connected");
          connection.classList.add("disconnected");
        }
        break;
    }
  }

  // Show/hide Stackmat status
  function showStackmatStatus(show) {
    if (stackmatStatus) {
      if (show) {
        stackmatStatus.classList.add("show");
        // Show initial setup message
        if (stackmatConnectionText) {
          // Get translations
          const translations = window.i18nModule?.translations || {};
          const stackmatTranslations = translations.stackmat || {};

          stackmatConnectionText.textContent =
            stackmatTranslations.settingUp || "Setting up...";
        }
      } else {
        stackmatStatus.classList.remove("show");
      }
    }
  }

  // Decimal places selector
  const decimalPlacesSelector = document.getElementById(
    "decimal-places-selector"
  );
  if (decimalPlacesSelector) {
    decimalPlacesSelector.addEventListener("change", function () {
      const decimalPlaces = this.value;
      localStorage.setItem("scTimer-decimalPlaces", decimalPlaces);

      // Update timer display immediately if timer is idle
      if (timerState === TIMER_STATE.IDLE) {
        const currentTime = getPreviousTime();
        timerElement.textContent = formatTime(currentTime);
        updateDisplay(currentTime);
      }

      // Update all times in the times list (preserving original precision)
      updateTimesList();

      // Update statistics display (preserving original precision)
      updateStats();

      // Note: We don't modify the stored time values - only the display format changes
      // This preserves the original precision of all recorded times
    });
  }

  // Function to toggle manual input timer
  function toggleManualInputTimer(show) {
    if (show) {
      // Hide the regular timer and show the manual input
      timerElement.style.display = "none";
      if (manualInputContainer) {
        manualInputContainer.style.display = "flex";
      }

      // Focus the input field
      if (manualTimeInput) {
        setTimeout(() => {
          manualTimeInput.focus();
        }, 100);
      }

      // Don't start inspection automatically - wait for user input
    } else {
      // Show the regular timer and hide the manual input
      timerElement.style.display = "block";
      if (manualInputContainer) {
        manualInputContainer.style.display = "none";
      }

      // Clear the inspection display
      if (manualInspection) {
        manualInspection.textContent = "";
      }

      // Stop inspection if it's running
      stopInspection();
    }
  }

  // Function to toggle Stackmat reset inspection setting visibility
  function toggleStackmatResetInspectionSetting(show) {
    if (
      stackmatResetInspectionCheckbox &&
      stackmatResetInspectionCheckbox.parentElement
    ) {
      const settingElement =
        stackmatResetInspectionCheckbox.parentElement.parentElement;

      if (show) {
        settingElement.style.display = "block";
        stackmatResetInspectionCheckbox.disabled = false;
        stackmatResetInspectionCheckbox.parentElement.classList.remove(
          "disabled"
        );
      } else {
        settingElement.style.display = "none";
        stackmatResetInspectionCheckbox.disabled = true;
        stackmatResetInspectionCheckbox.parentElement.classList.add("disabled");
      }
    }
  }

  // Function to toggle microphone selector visibility
  function toggleMicrophoneSelectorVisibility(show) {
    if (microphoneSelector && microphoneSelector.parentElement) {
      const settingElement = microphoneSelector.parentElement.parentElement;

      if (show) {
        settingElement.style.display = "block";
        microphoneSelector.disabled = false;
        microphoneSelector.parentElement.classList.remove("disabled");

        // Populate microphone options when shown
        populateMicrophoneOptions();
      } else {
        settingElement.style.display = "none";
        microphoneSelector.disabled = true;
        microphoneSelector.parentElement.classList.add("disabled");
      }
    }
  }

  // Function to populate microphone options
  async function populateMicrophoneOptions() {
    if (!microphoneSelector) return;

    try {
      // Get translations
      const translations = window.i18nModule?.translations || {};
      const timerOptionsTranslations = translations.timerOptions || {};

      // Clear existing options except auto-detect
      microphoneSelector.innerHTML = `
        <option value="auto">${
          timerOptionsTranslations.microphoneAuto || "Auto-detect"
        }</option>
      `;

      // Request permission first to get device labels
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: true,
        });
        stream.getTracks().forEach((track) => track.stop()); // Stop immediately
      } catch (error) {
        console.warn(
          "Could not get microphone permission for device enumeration:",
          error
        );
      }

      // Get available audio input devices
      if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const audioInputs = devices.filter(
          (device) => device.kind === "audioinput"
        );

        audioInputs.forEach((device, index) => {
          const option = document.createElement("option");
          option.value = device.deviceId;
          option.textContent = device.label || `Microphone ${index + 1}`;
          microphoneSelector.appendChild(option);
        });

        // Restore saved selection
        const savedMicrophone = localStorage.getItem(
          "scTimer-selectedMicrophone"
        );
        if (savedMicrophone) {
          microphoneSelector.value = savedMicrophone;
        }
      }
    } catch (error) {
      console.warn("Could not enumerate audio devices:", error);
    }
  }

  // Manual time input submission
  if (manualTimeInput) {
    // Allow Enter key to submit and prevent space key
    manualTimeInput.addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        submitManualTime();
        return;
      }

      // Prevent space key in the input field
      if (e.key === " " || e.code === "Space") {
        e.preventDefault();

        // Start inspection if it's not already running
        const isManualInputMode =
          timerModeSelector && timerModeSelector.value === "typing";
        if (
          isManualInputMode &&
          useInspectionCheckbox &&
          useInspectionCheckbox.checked
        ) {
          if (!inspectionInterval) {
            startInspection();
          }
        }
        return;
      }

      // Input validation: only allow numbers 0-9, dots (.), and colons (:)
      const allowedChars = /[0-9.:]/;
      if (!allowedChars.test(e.key)) {
        e.preventDefault();
        return;
      }
    });

    // No automatic formatting in input - formatting happens during save
  }

  // Add click event for manual input container to start inspection
  if (manualInputContainer) {
    manualInputContainer.addEventListener("click", function (e) {
      // Only handle clicks on the container itself or the wrapper, not on the input
      if (
        e.target === manualInputContainer ||
        e.target.classList.contains("manual-input-wrapper")
      ) {
        const isManualInputMode =
          timerModeSelector && timerModeSelector.value === "typing";
        if (
          isManualInputMode &&
          useInspectionCheckbox &&
          useInspectionCheckbox.checked
        ) {
          // Only start inspection if it's not already running
          if (!inspectionInterval) {
            startInspection();
          }
        }
      }
    });
  }

  // Function to submit manual time
  function submitManualTime() {
    if (!manualTimeInput) return;

    let timeStr = manualTimeInput.value.trim();
    if (!timeStr) return;

    // Check if input contains only digits (integer mode)
    if (/^\d+$/.test(timeStr)) {
      // Apply shifting decimal formatting for storage
      timeStr = formatIntegerInput(timeStr);
    }

    // Parse the time string to milliseconds
    let timeMs = 0;

    // Handle different formats: 12.345, 1:23.45, 12:34.56
    if (timeStr.includes(":")) {
      const parts = timeStr.split(":");
      if (parts.length === 2) {
        // Format: MM:SS.ms
        const minutes = parseFloat(parts[0]);
        const seconds = parseFloat(parts[1]);
        timeMs = (minutes * 60 + seconds) * 1000;
      } else if (parts.length === 3) {
        // Format: HH:MM:SS.ms
        const hours = parseFloat(parts[0]);
        const minutes = parseFloat(parts[1]);
        const seconds = parseFloat(parts[2]);
        timeMs = (hours * 3600 + minutes * 60 + seconds) * 1000;
      }
    } else {
      // Format: SS.ms
      timeMs = parseFloat(timeStr) * 1000;
    }

    // Validate the time
    if (isNaN(timeMs) || timeMs <= 0) {
      // Get translations
      const translations = window.i18nModule?.translations || {};
      const modalTranslations = translations.modals || {};

      import("./modal-manager.js").then(({ showAlert }) => {
        showAlert(
          "Please enter a valid time format (e.g., 12.345, 1:23.45)",
          modalTranslations.error || "Error"
        );
      });
      return;
    }

    // Save the time
    saveTime(timeMs);

    // Clear the input field
    manualTimeInput.value = "";

    // Generate a new scramble
    generateScramble();

    // Focus the input field again
    setTimeout(() => {
      manualTimeInput.focus();
    }, 100);
  }

  // Load dark mode setting on startup
  const darkMode = localStorage.getItem("scTimer-darkMode");
  if (darkMode === "true") {
    darkModeCheckbox.checked = true;
    document.body.classList.add("dark-mode");
  }

  // Load FMC keyboard setting on startup
  if (showFMCKeyboardCheckbox) {
    const showFMCKeyboard = localStorage.getItem("scTimer-showFMCKeyboard");
    if (showFMCKeyboard === "false") {
      showFMCKeyboardCheckbox.checked = false;
    } else {
      // Default to true if not set
      showFMCKeyboardCheckbox.checked = true;
    }

    // Update the label state based on current event
    updateFMCKeyboardLabelState();
  }

  // Load inspection setting on startup
  if (useInspectionCheckbox) {
    // Check if current event is a blind event
    const isBlindEvent = ["333bf", "444bf", "555bf", "333mbf"].includes(
      currentEvent
    );

    if (isBlindEvent) {
      // Disable inspection for blind events
      useInspectionCheckbox.disabled = true;
      useInspectionCheckbox.checked = false;
      useInspectionCheckbox.parentElement.classList.add("disabled");
    } else {
      // For non-blind events, always set to checked
      useInspectionCheckbox.checked = true;
    }
  }

  // Load inspection sound setting on startup
  if (inspectionSoundSelector) {
    // Check if current event is a blind event
    const isBlindEvent = ["333bf", "444bf", "555bf", "333mbf"].includes(
      currentEvent
    );

    if (isBlindEvent) {
      // Disable inspection sounds for blind events
      inspectionSoundSelector.disabled = true;
      inspectionSoundSelector.value = "none";
      if (inspectionSoundSelector.parentElement) {
        inspectionSoundSelector.parentElement.classList.add("disabled");
      }
    } else {
      // For non-blind events, load saved setting
      const inspectionSound =
        localStorage.getItem("scTimer-inspectionSound") || "beep";
      inspectionSoundSelector.value = inspectionSound;

      // Also check if inspection is enabled to set dropdown state
      const inspectionEnabled =
        localStorage.getItem("scTimer-inspection") !== "false";
      inspectionSoundSelector.disabled = !inspectionEnabled;
      if (inspectionSoundSelector.parentElement) {
        if (inspectionEnabled) {
          inspectionSoundSelector.parentElement.classList.remove("disabled");
        } else {
          inspectionSoundSelector.parentElement.classList.add("disabled");
        }
      }
    }
  }

  // Load timer mode setting on startup
  if (timerModeSelector) {
    const timerMode = localStorage.getItem("scTimer-timerMode") || "timer";
    timerModeSelector.value = timerMode;

    // Apply the timer mode
    if (timerMode === "typing") {
      toggleManualInputTimer(true);
    } else if (timerMode === "stackmat") {
      // Initialize Stackmat mode
      handleStackmatModeChange(true);
    } else if (timerMode === "bluetooth") {
      // Bluetooth mode is not implemented yet, revert to timer mode
      timerModeSelector.value = "timer";
      localStorage.setItem("scTimer-timerMode", "timer");
    }

    // Set Stackmat settings visibility (with delay to ensure translations are loaded)
    setTimeout(() => {
      toggleStackmatResetInspectionSetting(timerMode === "stackmat");
      toggleMicrophoneSelectorVisibility(timerMode === "stackmat");
    }, 100);
  }

  // Load decimal places setting on startup
  if (decimalPlacesSelector) {
    const decimalPlaces = localStorage.getItem("scTimer-decimalPlaces") || "3";
    decimalPlacesSelector.value = decimalPlaces;

    // Set initial state based on current event
    updateDecimalPlacesSelectorState(currentEvent === "333fm");
  }

  // Load custom sessions first (needed for event restoration)
  loadCustomSessions();

  // Restore last selected event and session
  restoreLastEventAndSession();

  // Restore times list state
  restoreTimesListState();

  // Generate initial scramble after event restoration
  // Use setTimeout to ensure event restoration is complete
  setTimeout(() => {
    generateScramble();
  }, 200);

  // Function to save current event and session state
  function saveCurrentEventAndSession() {
    localStorage.setItem("scTimer-lastEvent", currentEvent);
    localStorage.setItem("scTimer-lastEventText", currentEventText.textContent);
    if (currentSessionId) {
      localStorage.setItem("scTimer-lastSessionId", currentSessionId);
    } else {
      localStorage.removeItem("scTimer-lastSessionId");
    }
  }

  // Function to save times list state
  function saveTimesListState() {
    const isOpen = timesPanel.classList.contains("show");
    localStorage.setItem("scTimer-timesListOpen", isOpen.toString());
  }

  // Function to restore last event and session
  function restoreLastEventAndSession() {
    const lastEvent = localStorage.getItem("scTimer-lastEvent");
    const lastEventText = localStorage.getItem("scTimer-lastEventText");
    const lastSessionId = localStorage.getItem("scTimer-lastSessionId");

    if (lastEvent && lastEvent !== currentEvent) {
      // Use setTimeout to ensure DOM is ready and event handlers are attached
      setTimeout(() => {
        if (lastSessionId) {
          // Restore custom session
          const sessionOption = document.querySelector(
            `.event-option.custom-session[data-event="${lastEvent}"][data-session-id="${lastSessionId}"]`
          );
          if (sessionOption) {
            sessionOption.click();

            // Ensure the text is updated correctly after the click for custom sessions
            setTimeout(() => {
              if (lastEventText) {
                currentEventText.textContent = lastEventText;
              } else {
                // Fallback: extract text from the session option if saved text is not available
                // For custom sessions, the text is usually just the textContent of the session option
                const sessionText = sessionOption.textContent.trim();
                if (sessionText) {
                  currentEventText.textContent = sessionText;
                }
              }
            }, 50);
            return;
          }
        } else {
          // Restore default event
          const eventOption = document.querySelector(
            `.event-option[data-event="${lastEvent}"]`
          );
          if (eventOption) {
            eventOption.click();

            // Ensure the text is updated correctly after the click
            setTimeout(() => {
              if (lastEventText) {
                currentEventText.textContent = lastEventText;
              } else {
                // Fallback: extract text from the event option if saved text is not available
                const textSpan = eventOption.querySelector("span[data-i18n]");
                if (textSpan) {
                  currentEventText.textContent = textSpan.textContent.trim();
                }
              }
            }, 50);
            return;
          }
        }
      }, 100);
    }
  }

  // Function to restore times list state
  function restoreTimesListState() {
    const wasOpen = localStorage.getItem("scTimer-timesListOpen");
    if (wasOpen === "true") {
      // Open the times panel
      timesPanel.classList.add("show");

      // Add class to body for large screen layout
      if (window.innerWidth >= 1024) {
        document.body.classList.add("panel-open");
        const container = document.querySelector(".container");
        container.style.marginLeft = "350px";
        container.style.width = "calc(100% - 350px)";
        container.style.transform = "scale(0.9)";
        container.style.transformOrigin = "center center";
      }

      // Update toggle button
      timesToggle.classList.add("active");
      const icon = timesToggle.querySelector("i");
      if (icon) {
        icon.classList.remove("fa-history");
        icon.classList.add("fa-times");
      }

      // Update toggle position
      updateTimesTogglePosition();
    }
  }

  // Scramble font size slider
  if (scrambleFontSizeSlider && scrambleFontSizeValue) {
    // Load saved font size or set default
    const savedFontSize = localStorage.getItem("scTimer-scrambleFontSize");
    if (savedFontSize) {
      scrambleFontSizeSlider.value = savedFontSize;
      scrambleFontSizeValue.textContent = savedFontSize + "×";
      updateScrambleFontSize(savedFontSize);
    } else {
      // Default value is 1.0
      updateScrambleFontSize(1.0);
    }

    // Update font size when slider changes
    scrambleFontSizeSlider.addEventListener("input", function () {
      const value = parseFloat(this.value);
      scrambleFontSizeValue.textContent = value.toFixed(1) + "×";
      updateScrambleFontSize(value);
      localStorage.setItem("scTimer-scrambleFontSize", value);
    });
  }

  // Function to update scramble font size
  function updateScrambleFontSize(scaleFactor) {
    // Get all scramble elements
    const scrambleElements = document.querySelectorAll(
      ".scramble, .solve-details-scramble"
    );

    scrambleElements.forEach((element) => {
      // Get the current event class
      let eventClass = "";
      element.classList.forEach((cls) => {
        if (cls.startsWith("event-")) {
          eventClass = cls;
        }
      });

      // Apply the scale factor as a CSS variable
      element.style.setProperty("--font-scale", scaleFactor);
    });

    // Update CSS variables for all puzzle types
    document.documentElement.style.setProperty("--font-scale", scaleFactor);
  }

  // Function to update FMC keyboard label state
  function updateFMCKeyboardLabelState() {
    if (!showFMCKeyboardLabel) return;

    // Enable/disable the checkbox based on current event
    if (currentEvent === "333fm") {
      showFMCKeyboardLabel.classList.remove("disabled");
      showFMCKeyboardCheckbox.disabled = false;
    } else {
      showFMCKeyboardLabel.classList.add("disabled");
      showFMCKeyboardCheckbox.disabled = true;
    }
  }

  // Function to update FMC keyboard visibility
  function updateFMCKeyboardVisibility(show) {
    // This function will be implemented in the FMC manager
    if (typeof window.updateFMCKeyboardVisibility === "function") {
      window.updateFMCKeyboardVisibility(show);
    }
  }

  // New Session Modal Functions
  const newSessionModal = document.getElementById("new-session-modal");
  const newSessionClose = document.getElementById("new-session-close");
  const newSessionSave = document.getElementById("new-session-save");
  const sessionNameInput = document.getElementById("session-name");
  const sessionPuzzleSelect = document.getElementById("session-puzzle");

  // Function to show the new session modal
  function showNewSessionModal() {
    if (!newSessionModal) return;

    // Reset form fields
    if (sessionNameInput) {
      sessionNameInput.value = "My Session";
    }

    if (sessionPuzzleSelect) {
      sessionPuzzleSelect.value = "333"; // Default to 3x3
    }

    // Show the modal
    newSessionModal.classList.add("show");

    // Focus the session name input
    if (sessionNameInput) {
      setTimeout(() => {
        sessionNameInput.focus();
        sessionNameInput.select();
      }, 100);
    }
  }

  // Function to hide the new session modal
  function hideNewSessionModal() {
    if (!newSessionModal) return;
    newSessionModal.classList.remove("show");
  }

  // Function to create a new session
  function createNewSession() {
    const sessionName = sessionNameInput.value.trim() || "My Session";
    const puzzleType = sessionPuzzleSelect.value;

    // Get the puzzle name from the selected option
    const puzzleName =
      sessionPuzzleSelect.options[sessionPuzzleSelect.selectedIndex]
        .textContent;

    // Creating new session

    // Create a unique session ID
    const sessionId = Date.now().toString();

    // Create a new session object
    const newSession = {
      id: sessionId,
      name: sessionName,
      puzzleType: puzzleType,
      created: new Date().toISOString(),
    };

    // Add to custom sessions array
    customSessions.push(newSession);

    // Save to localStorage
    saveCustomSessions();

    // Update the dropdown
    updateCustomSessionsInDropdown();

    // Update stats dropdown if it exists and select the new session
    if (
      document.getElementById("stats-details-panel") &&
      document.getElementById("stats-details-panel").classList.contains("show")
    ) {
      populateEventSelector();
      // Select the newly created session in the stats dropdown
      selectStatsEvent(puzzleType, sessionId, sessionName);
    }

    // Update button text and icon
    currentEventText.textContent = sessionName;

    // Set the appropriate icon class
    // For non-WCA puzzles, use the unofficial- prefix for icons only
    const nonWcaPuzzleIcons = {
      fto: "unofficial-fto",
      master_tetraminx: "unofficial-mtetram",
      kilominx: "unofficial-kilominx",
      redi_cube: "unofficial-redi",
      baby_fto: "unofficial-baby_fto",
    };

    if (nonWcaPuzzleIcons[puzzleType] !== undefined) {
      // For non-WCA puzzles with icons, use the unofficial- prefix
      if (nonWcaPuzzleIcons[puzzleType]) {
        currentEventIcon.className = `cubing-icon ${nonWcaPuzzleIcons[puzzleType]}`;
      } else {
        // For puzzles without icons, just use a generic icon or none
        currentEventIcon.className = ""; // No icon
      }
    } else {
      // For WCA events, use the event- prefix
      currentEventIcon.className = `cubing-icon event-${puzzleType}`;
    }

    // Update current event and session ID
    currentEvent = puzzleType;
    currentEventElement.textContent = puzzleType;
    currentSessionId = sessionId;

    // Initialize the times array for this session if it doesn't exist
    const storageKey = `session_${sessionId}`;
    if (!timesMap[storageKey]) {
      timesMap[storageKey] = [];
    }

    // Update FMC keyboard label state
    updateFMCKeyboardLabelState();

    // Process the event change
    processEventChange(puzzleType, sessionName);

    // Save the current event and session state
    saveCurrentEventAndSession();

    // Hide the modal
    hideNewSessionModal();

    // Show a confirmation message
    import("./modal-manager.js").then(({ showAlert }) => {
      showAlert(
        `New session "${sessionName}" created with puzzle type: ${puzzleName}`
      );
    });
  }

  // Event listeners for the new session modal
  if (newSessionClose) {
    newSessionClose.addEventListener("click", hideNewSessionModal);
  }

  if (newSessionSave) {
    newSessionSave.addEventListener("click", createNewSession);
  }

  // Close modal when clicking outside
  if (newSessionModal) {
    newSessionModal.addEventListener("click", function (event) {
      if (event.target === newSessionModal) {
        hideNewSessionModal();
      }
    });
  }

  // Allow Enter key to submit the form
  if (sessionNameInput) {
    sessionNameInput.addEventListener("keypress", function (event) {
      if (event.key === "Enter") {
        createNewSession();
      }
    });
  }

  // Listen for the FMC result saved event
  document.addEventListener("fmcResultSaved", function handleFMCResult(e) {
    // Save the FMC result to the solve list
    if (e.detail) {
      saveTime(0, null, e.detail);
    }

    // Generate new FMC scramble
    generateFMCScramble().then((scramble) => {
      currentScramble = scramble;

      // Update scramble display
      if (scrambleElement) {
        const scrambleText = document.getElementById("scramble-text");
        if (scrambleText) {
          scrambleText.textContent = scramble;
        }
      }

      // Update visualization
      updateVisualization();
    });
  });

  // Statistics Details Panel functionality
  const statsDetailsPanel = document.getElementById("stats-details-panel");

  // Add click event to stats container to open details panel
  if (statsContainer) {
    statsContainer.addEventListener("click", function () {
      openStatsDetails();
    });
  }

  // Add click event to stats button in times panel footer
  const statsBtn = document.getElementById("stats-btn");
  if (statsBtn) {
    statsBtn.addEventListener("click", function () {
      openStatsDetails();
    });
  }

  // Variable to track if times list was open before stats modal
  let timesListWasOpen = false;

  // Function to open statistics details panel
  function openStatsDetails() {
    if (statsDetailsPanel) {
      // Check if times panel is currently open and remember its state
      const timesPanel = document.getElementById("times-panel");
      timesListWasOpen = timesPanel && timesPanel.classList.contains("show");

      // Trigger the list toggler normally if times panel is open
      if (timesListWasOpen) {
        // Trigger the normal times toggle handler to close it properly
        timesToggleHandler();
      }

      statsDetailsPanel.classList.add("show");
      populateEventSelector();
      // Reset to main dropdown selection after populate (to override any previous selections)
      resetStatsModalSelection();
      // Force update stats details after reset
      updateStatsDetails();
      updateSessionButtons();

      // Initialize import/export buttons
      setTimeout(() => {
        initImportExportButtons();
        initSessionManagementButtons();
      }, 100);

      // Change times toggle to close the stats details panel
      if (timesToggle) {
        const icon = timesToggle.querySelector("i");
        if (icon) {
          icon.className = "fas fa-times";
        }
        timesToggle.title = "Close Statistics";

        // Remove existing event listeners and add new one for stats close
        timesToggle.removeEventListener("click", timesToggleHandler);
        timesToggle.addEventListener("click", closeStatsDetails);
      }

      // Add event listener to the close button
      const statsCloseBtn = document.getElementById("stats-details-close");
      if (statsCloseBtn) {
        statsCloseBtn.addEventListener("click", closeStatsDetails);
      }
    }
  }

  // Function to reset stats modal selection to main dropdown
  function resetStatsModalSelection() {
    const statsEventBtn = document.getElementById("stats-event-selector-btn");
    if (statsEventBtn) {
      // Always reset to current main dropdown selection, ignoring any "all-*" selections
      if (currentSessionId) {
        // Find the current session in custom sessions
        const currentSession = customSessions.find(
          (s) => s.id === currentSessionId
        );
        if (currentSession) {
          // Set to current custom session
          statsEventBtn.setAttribute("data-event", currentSession.puzzleType);
          statsEventBtn.setAttribute("data-session-id", currentSessionId);
          statsEventBtn.setAttribute("data-session-name", currentSession.name);
          // Update display to show custom session
          updateStatsEventDisplay(
            currentSession.puzzleType,
            currentSessionId,
            currentSession.name
          );
        } else {
          // Fallback to current event
          statsEventBtn.setAttribute("data-event", currentEvent);
          statsEventBtn.removeAttribute("data-session-id");
          statsEventBtn.removeAttribute("data-session-name");
          // Update display to show current event
          updateStatsEventDisplay(currentEvent);
        }
      } else {
        // Set to current regular event (never "all-*" values)
        statsEventBtn.setAttribute("data-event", currentEvent);
        statsEventBtn.removeAttribute("data-session-id");
        statsEventBtn.removeAttribute("data-session-name");
        // Update display to show current event
        updateStatsEventDisplay(currentEvent);
      }

      // Force switch back to single event view (in case we were in all events view)
      const singleStatsView = document.getElementById("single-stats-view");
      const allEventsView = document.getElementById("all-events-view");
      if (singleStatsView) singleStatsView.style.display = "block";
      if (allEventsView) allEventsView.style.display = "none";
    }
  }

  // Function to close statistics details panel
  function closeStatsDetails() {
    if (statsDetailsPanel) {
      statsDetailsPanel.classList.remove("show");

      // Reset stats modal selection to main dropdown when closing
      resetStatsModalSelection();

      // Remove event listener from the close button
      const statsCloseBtn = document.getElementById("stats-details-close");
      if (statsCloseBtn) {
        statsCloseBtn.removeEventListener("click", closeStatsDetails);
      }

      // Restore times toggle to original state
      if (timesToggle) {
        // Remove stats close listener and restore original times toggle handler
        timesToggle.removeEventListener("click", closeStatsDetails);
        timesToggle.addEventListener("click", timesToggleHandler);

        // If times list was previously open, restore it
        if (timesListWasOpen) {
          // Trigger the times toggle handler to open the list
          timesToggleHandler();
          // Reset the flag
          timesListWasOpen = false;
        } else {
          // Just restore the icon and title for closed state
          const icon = timesToggle.querySelector("i");
          if (icon) {
            icon.className = "fas fa-history";
          }
          timesToggle.title = "View Times";
        }
      }
    }
  }

  // Function to populate event selector
  function populateEventSelector() {
    const eventDropdown = document.getElementById("stats-event-dropdown");
    const eventBtn = document.getElementById("stats-event-selector-btn");

    if (!eventDropdown || !eventBtn) return;

    // Clear existing options
    eventDropdown.innerHTML = "";

    // Get all available events from the main event dropdown
    const mainEventDropdown = document.getElementById("event-dropdown");
    if (mainEventDropdown) {
      // Add special overview options first
      const allWCAOption = document.createElement("div");
      allWCAOption.className = "stats-event-option";
      allWCAOption.setAttribute("data-event", "all-wca");
      allWCAOption.innerHTML =
        '<i class="fas fa-trophy"></i><p>&nbsp;&nbsp;</p><span>All WCA Events</span>';
      allWCAOption.addEventListener("click", () => {
        selectStatsEvent("all-wca");
      });
      eventDropdown.appendChild(allWCAOption);

      const allNonWCAOption = document.createElement("div");
      allNonWCAOption.className = "stats-event-option";
      allNonWCAOption.setAttribute("data-event", "all-non-wca");
      allNonWCAOption.innerHTML =
        '<i class="fas fa-cubes"></i><p>&nbsp;&nbsp;</p><span>All Non-WCA Events</span>';
      allNonWCAOption.addEventListener("click", () => {
        selectStatsEvent("all-non-wca");
      });
      eventDropdown.appendChild(allNonWCAOption);

      const allEventsOption = document.createElement("div");
      allEventsOption.className = "stats-event-option";
      allEventsOption.setAttribute("data-event", "all-events");
      allEventsOption.innerHTML =
        '<i class="fas fa-globe"></i><p>&nbsp;&nbsp;</p><span>All Events</span>';
      allEventsOption.addEventListener("click", () => {
        selectStatsEvent("all-events");
      });
      eventDropdown.appendChild(allEventsOption);

      // Add separator
      const separator = document.createElement("div");
      separator.className = "stats-event-divider";
      eventDropdown.appendChild(separator);

      // Copy options from main event dropdown, excluding dividers and special options
      const eventOptions = mainEventDropdown.querySelectorAll(
        ".event-option:not(.custom-session)"
      );
      eventOptions.forEach((option) => {
        // Skip new session options
        if (option.classList.contains("new-session-option")) {
          return;
        }

        const eventId = option.getAttribute("data-event");
        if (!eventId) return;

        // Create option div with same structure as main dropdown
        const optionDiv = document.createElement("div");
        optionDiv.className = "stats-event-option";
        optionDiv.setAttribute("data-event", eventId);

        // Copy the icon span
        const iconSpan = option.querySelector(".cubing-icon");
        if (iconSpan) {
          const newIconSpan = document.createElement("span");
          newIconSpan.className = iconSpan.className;
          optionDiv.appendChild(newIconSpan);
        }

        // Copy the text span
        const textSpan = option.querySelector("span[data-i18n]");
        if (textSpan) {
          const newTextSpan = document.createElement("span");
          newTextSpan.setAttribute(
            "data-i18n",
            textSpan.getAttribute("data-i18n")
          );
          newTextSpan.textContent = textSpan.textContent;
          optionDiv.appendChild(newTextSpan);
        }

        // Add click event listener
        optionDiv.addEventListener("click", function () {
          selectStatsEvent(eventId);
        });

        eventDropdown.appendChild(optionDiv);
      });

      // Check if there are custom sessions to add divider
      const customSessionOptions =
        mainEventDropdown.querySelectorAll(".custom-session");

      if (customSessionOptions.length > 0) {
        // Add divider
        const divider = document.createElement("div");
        divider.className = "stats-event-divider";
        eventDropdown.appendChild(divider);
      }

      // Add custom sessions
      customSessionOptions.forEach((sessionOption) => {
        const optionDiv = document.createElement("div");
        optionDiv.className = "stats-event-option custom-session";
        optionDiv.setAttribute(
          "data-event",
          sessionOption.getAttribute("data-event")
        );
        optionDiv.setAttribute(
          "data-session-id",
          sessionOption.getAttribute("data-session-id")
        );
        optionDiv.setAttribute(
          "data-session-name",
          sessionOption.getAttribute("data-session-name")
        );

        // Copy the icon span
        const iconSpan = sessionOption.querySelector(".cubing-icon");
        if (iconSpan) {
          const newIconSpan = document.createElement("span");
          newIconSpan.className = iconSpan.className;
          optionDiv.appendChild(newIconSpan);
        }

        // Copy the text span (session name)
        const textSpan = sessionOption.querySelector("span:not(.cubing-icon)");
        if (textSpan) {
          const newTextSpan = document.createElement("span");
          newTextSpan.textContent = textSpan.textContent;
          optionDiv.appendChild(newTextSpan);
        }

        // Add click event listener
        optionDiv.addEventListener("click", function () {
          const sessionId = this.getAttribute("data-session-id");
          const sessionName = this.getAttribute("data-session-name");
          selectStatsEvent(
            this.getAttribute("data-event"),
            sessionId,
            sessionName
          );
        });

        eventDropdown.appendChild(optionDiv);
      });

      // Add separator and "Create New Session" option
      const createSeparator = document.createElement("div");
      createSeparator.className = "stats-event-divider";
      eventDropdown.appendChild(createSeparator);

      const createOption = document.createElement("div");
      createOption.className = "stats-event-option create-session-option";

      const createIcon = document.createElement("span");
      createIcon.innerHTML = '<i class="fas fa-plus"></i>';

      const createText = document.createElement("span");
      // Get translations
      const translations = window.i18nModule?.translations || {};
      const sessionsTranslations = translations.sessions || {};
      createText.textContent =
        sessionsTranslations.newSessionTitle || "New Session";

      createOption.appendChild(createIcon);
      createOption.appendChild(createText);

      createOption.addEventListener("click", () => {
        // Close the dropdown
        eventDropdown.classList.remove("show");
        // Open the new session modal
        showNewSessionModal();
      });

      eventDropdown.appendChild(createOption);
    }

    // Set current selection based on current session
    if (currentSessionId) {
      // Find the current session in custom sessions
      const currentSession = customSessions.find(
        (s) => s.id === currentSessionId
      );
      if (currentSession) {
        updateStatsEventDisplay(
          currentSession.puzzleType,
          currentSessionId,
          currentSession.name
        );
        // Also set the button attributes
        const statsEventBtn = document.getElementById(
          "stats-event-selector-btn"
        );
        if (statsEventBtn) {
          statsEventBtn.setAttribute("data-event", currentSession.puzzleType);
          statsEventBtn.setAttribute("data-session-id", currentSessionId);
          statsEventBtn.setAttribute("data-session-name", currentSession.name);
        }
      } else {
        updateStatsEventDisplay(currentEvent);
        // Set button attributes for regular event
        const statsEventBtn = document.getElementById(
          "stats-event-selector-btn"
        );
        if (statsEventBtn) {
          statsEventBtn.setAttribute("data-event", currentEvent);
          statsEventBtn.removeAttribute("data-session-id");
          statsEventBtn.removeAttribute("data-session-name");
        }
      }
    } else {
      updateStatsEventDisplay(currentEvent);
      // Set button attributes for regular event
      const statsEventBtn = document.getElementById("stats-event-selector-btn");
      if (statsEventBtn) {
        statsEventBtn.setAttribute("data-event", currentEvent);
        statsEventBtn.removeAttribute("data-session-id");
        statsEventBtn.removeAttribute("data-session-name");
      }
    }

    // Remove existing event listeners to prevent duplicates
    const existingClickHandler = eventBtn._statsClickHandler;
    if (existingClickHandler) {
      eventBtn.removeEventListener("click", existingClickHandler);
    }

    // Create new click handler and store reference
    const clickHandler = function (e) {
      e.stopPropagation();
      eventDropdown.classList.toggle("show");
    };
    eventBtn._statsClickHandler = clickHandler;
    eventBtn.addEventListener("click", clickHandler);

    // Remove existing document click listener to prevent duplicates
    if (document._statsDocumentClickHandler) {
      document.removeEventListener(
        "click",
        document._statsDocumentClickHandler
      );
    }

    // Create new document click handler and store reference
    const documentClickHandler = function (e) {
      // Don't close if clicking on the button or dropdown
      if (!eventBtn.contains(e.target) && !eventDropdown.contains(e.target)) {
        eventDropdown.classList.remove("show");
      }
    };
    document._statsDocumentClickHandler = documentClickHandler;
    document.addEventListener("click", documentClickHandler);
  }

  // Function to select stats event
  function selectStatsEvent(eventValue, sessionId = null, sessionName = null) {
    const eventDropdown = document.getElementById("stats-event-dropdown");
    const statsEventBtn = document.getElementById("stats-event-selector-btn");

    eventDropdown.classList.remove("show");

    // Store session data in button attributes
    if (statsEventBtn) {
      if (sessionId && sessionName) {
        statsEventBtn.setAttribute("data-event", eventValue);
        statsEventBtn.setAttribute("data-session-id", sessionId);
        statsEventBtn.setAttribute("data-session-name", sessionName);
      } else {
        // Clear session attributes for regular events
        statsEventBtn.removeAttribute("data-session-id");
        statsEventBtn.removeAttribute("data-session-name");
        statsEventBtn.setAttribute("data-event", eventValue);
        // Also clear any data-session-name attribute that might be lingering
        if (statsEventBtn.hasAttribute("data-session-name")) {
          statsEventBtn.removeAttribute("data-session-name");
        }
      }
    }

    updateStatsEventDisplay(eventValue, sessionId, sessionName);
    updateStatsDetails();
    updateSessionButtons();

    // Show appropriate view based on selection
    const singleStatsView = document.getElementById("single-stats-view");
    const allEventsView = document.getElementById("all-events-view");

    if (eventValue.startsWith("all-")) {
      // Show all events view
      if (singleStatsView) singleStatsView.style.display = "none";
      if (allEventsView) allEventsView.style.display = "block";
      populateAllEventsView(eventValue);
    } else {
      // Show single event view
      if (singleStatsView) singleStatsView.style.display = "block";
      if (allEventsView) allEventsView.style.display = "none";
    }
  }

  // Function to update stats event display
  function updateStatsEventDisplay(
    eventValue,
    sessionId = null,
    sessionName = null
  ) {
    const eventText = document.getElementById("stats-event-text");
    const eventIcon = document.getElementById("stats-event-icon");

    if (!eventText || !eventIcon) return;

    // Handle special "all-*" events
    if (eventValue.startsWith("all-")) {
      switch (eventValue) {
        case "all-wca":
          eventText.textContent = "All WCA Events";
          eventIcon.className = "fas fa-trophy";
          break;
        case "all-non-wca":
          eventText.textContent = "All Non-WCA Events";
          eventIcon.className = "fas fa-cubes";
          break;
        case "all-events":
          eventText.textContent = "All Events";
          eventIcon.className = "fas fa-globe";
          break;
      }
      return;
    }

    // If this is a custom session, use session name and event icon
    if (sessionId && sessionName) {
      eventText.textContent = sessionName;

      // Set icon based on event type
      const nonWcaPuzzleIcons = {
        fto: "unofficial-fto",
        master_tetraminx: "unofficial-mtetram",
        kilominx: "unofficial-kilominx",
        redi_cube: "unofficial-redi",
        baby_fto: "unofficial-baby_fto",
      };

      if (nonWcaPuzzleIcons[eventValue] !== undefined) {
        if (nonWcaPuzzleIcons[eventValue]) {
          eventIcon.className = `cubing-icon ${nonWcaPuzzleIcons[eventValue]}`;
        } else {
          eventIcon.className = "fas fa-cube stats-event-icon";
        }
      } else {
        eventIcon.className = `cubing-icon event-${eventValue}`;
      }
      return;
    }

    // Find the event option in main dropdown to get display text and icon
    const mainEventDropdown = document.getElementById("event-dropdown");
    if (mainEventDropdown) {
      const option = mainEventDropdown.querySelector(
        `[data-event="${eventValue}"]`
      );
      if (option) {
        const textSpan = option.querySelector("span[data-i18n]");
        if (textSpan) {
          eventText.textContent = textSpan.textContent;
        }

        // Update icon based on event
        const iconElement = option.querySelector(".cubing-icon");
        if (iconElement) {
          // Copy the cubing icon class
          eventIcon.className = iconElement.className;
        } else {
          // Default to cube icon
          eventIcon.className = "fas fa-cube stats-event-icon";
        }
      }
    }
  }

  // Function to get selected session metadata
  function getSelectedSessionMetadata() {
    let selectedEvent = currentEvent;
    let selectedSessionId = null;
    let selectedSessionName = null;

    // Always check the button attributes first (set by populateEventSelector)
    const statsEventBtn = document.getElementById("stats-event-selector-btn");
    if (statsEventBtn) {
      const sessionId = statsEventBtn.getAttribute("data-session-id");
      const sessionName = statsEventBtn.getAttribute("data-session-name");
      const eventFromBtn = statsEventBtn.getAttribute("data-event");

      if (sessionId && sessionName) {
        // This is a custom session
        selectedSessionId = sessionId;
        selectedSessionName = sessionName;
        selectedEvent = eventFromBtn || currentEvent;
      } else if (eventFromBtn) {
        // This is a regular event, use the event from button
        selectedEvent = eventFromBtn;
      }
      // If no data-event attribute, fall back to currentEvent (already set above)
    }

    return {
      selectedEvent,
      selectedSessionId,
      selectedSessionName,
    };
  }

  // Function to get selected session data (times array)
  function getSelectedSessionData() {
    const metadata = getSelectedSessionMetadata();

    // Return data based on selection
    if (metadata.selectedSessionId) {
      // Custom session
      const storageKey = `session_${metadata.selectedSessionId}`;
      return timesMap[storageKey] || [];
    } else {
      // Regular event - use the selected event directly, not currentSessionId
      // because we want to show stats for the selected event, not the current session
      const storageKey = metadata.selectedEvent;
      return timesMap[storageKey] || [];
    }
  }

  // Function to update statistics details
  function updateStatsDetails() {
    const times = getSelectedSessionData();

    // Update overview section
    updateOverviewStats(times);

    // Update averages section
    updateAveragesStats(times);

    // Update records section
    updateRecordsStats(times);

    // Update session analysis
    updateSessionAnalysis(times);

    // Update predictions
    updatePredictions(times);

    // Update charts
    updateCharts(times);
  }

  // Function to populate all events view
  function populateAllEventsView(viewType) {
    const allEventsList = document.getElementById("all-events-list");
    if (!allEventsList) return;

    // Clear existing content
    allEventsList.innerHTML = "";

    // Get all events based on view type
    let eventsToShow = [];

    // Define WCA events
    const wcaEvents = [
      "333",
      "222",
      "444",
      "555",
      "666",
      "777",
      "333bf",
      "333fm",
      "333oh",
      "clock",
      "minx",
      "pyram",
      "skewb",
      "sq1",
      "444bf",
      "555bf",
      "333mb",
    ];

    // Define non-WCA events (from main dropdown)
    const nonWcaEvents = [
      "fto",
      "master_tetraminx",
      "kilominx",
      "redi_cube",
      "baby_fto",
    ];

    switch (viewType) {
      case "all-wca":
        eventsToShow = wcaEvents.filter(
          (event) => timesMap[event] && timesMap[event].length > 0
        );
        break;
      case "all-non-wca":
        // Include non-WCA events and custom sessions
        eventsToShow = nonWcaEvents.filter(
          (event) => timesMap[event] && timesMap[event].length > 0
        );
        // Add custom sessions
        customSessions.forEach((session) => {
          const storageKey = `session_${session.id}`;
          if (timesMap[storageKey] && timesMap[storageKey].length > 0) {
            eventsToShow.push({
              id: storageKey,
              name: session.name,
              puzzleType: session.puzzleType,
              isCustom: true,
            });
          }
        });
        break;
      case "all-events":
        // Include all events with data
        eventsToShow = [...wcaEvents, ...nonWcaEvents].filter(
          (event) => timesMap[event] && timesMap[event].length > 0
        );
        // Add custom sessions
        customSessions.forEach((session) => {
          const storageKey = `session_${session.id}`;
          if (timesMap[storageKey] && timesMap[storageKey].length > 0) {
            eventsToShow.push({
              id: storageKey,
              name: session.name,
              puzzleType: session.puzzleType,
              isCustom: true,
            });
          }
        });
        break;
    }

    // Create event summary items
    eventsToShow.forEach((eventData) => {
      const isCustomSession =
        typeof eventData === "object" && eventData.isCustom;
      const eventId = isCustomSession ? eventData.id : eventData;
      const eventName = isCustomSession
        ? eventData.name
        : getEventDisplayName(eventData);
      const times = timesMap[eventId] || [];

      if (times.length === 0) return;

      // Calculate stats
      const validTimes = times.filter((t) => t.time !== Infinity);
      const bestTime =
        validTimes.length > 0
          ? Math.min(...validTimes.map((t) => t.time))
          : null;

      // Calculate best ao5 instead of current ao5
      let bestAo5 = null;
      if (times.length >= 5) {
        let bestAverage = Infinity;
        for (let i = 0; i <= times.length - 5; i++) {
          const avgTimes = times.slice(i, i + 5);
          const avg = calculateAverage(avgTimes);
          if (avg !== 0 && avg < bestAverage) {
            bestAverage = avg;
          }
        }
        bestAo5 = bestAverage !== Infinity ? bestAverage : null;
      }

      // Create event summary item
      const eventItem = document.createElement("div");
      eventItem.className = "event-summary-item";

      const eventNameDiv = document.createElement("div");
      eventNameDiv.className = "event-name";
      eventNameDiv.textContent = eventName;

      const eventStats = document.createElement("div");
      eventStats.className = "event-stats";

      // PB stat
      const pbStat = document.createElement("div");
      pbStat.className = "event-stat";
      pbStat.innerHTML = `
        <div class="event-stat-label">PB</div>
        <div class="event-stat-value ${bestTime ? "" : "no-data"}">${
        bestTime ? formatTime(bestTime) : "-"
      }</div>
      `;
      if (bestTime) {
        pbStat.addEventListener("click", () => {
          showStatDetailModal("best", times, 0, eventData);
        });
      }

      // Best AO5 stat
      const ao5Stat = document.createElement("div");
      ao5Stat.className = "event-stat";
      ao5Stat.innerHTML = `
        <div class="event-stat-label">ao5</div>
        <div class="event-stat-value ${
          bestAo5 && bestAo5 !== 0 ? "" : "no-data"
        }">${bestAo5 && bestAo5 !== 0 ? formatTime(bestAo5) : "-"}</div>
      `;
      if (bestAo5 && bestAo5 !== 0) {
        ao5Stat.addEventListener("click", () => {
          showStatDetailModal("best-ao5", times, 0, eventData);
        });
      }

      eventStats.appendChild(pbStat);
      eventStats.appendChild(ao5Stat);

      eventItem.appendChild(eventNameDiv);
      eventItem.appendChild(eventStats);

      allEventsList.appendChild(eventItem);
    });

    // If no events to show, display message
    if (eventsToShow.length === 0) {
      const noDataMessage = document.createElement("div");
      noDataMessage.className = "no-data-message";
      noDataMessage.style.textAlign = "center";
      noDataMessage.style.padding = "2rem";
      noDataMessage.style.color = "var(--text-secondary)";
      noDataMessage.textContent = "No solve data available for this category.";
      allEventsList.appendChild(noDataMessage);
    }
  }

  // Helper function to get event display name
  function getEventDisplayName(eventId) {
    const mainEventDropdown = document.getElementById("event-dropdown");
    if (mainEventDropdown) {
      const option = mainEventDropdown.querySelector(
        `[data-event="${eventId}"]`
      );
      if (option) {
        const textSpan = option.querySelector("span[data-i18n]");
        if (textSpan) {
          return textSpan.textContent;
        }
      }
    }
    return eventId; // Fallback to event ID
  }

  // Helper function to format date in dd/mm/yyyy format
  function formatDate(date) {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  }

  // Helper function to format integer input with shifting decimal places
  function formatIntegerInput(digits) {
    if (!digits) return "";

    // Get current decimal places setting
    const decimalPlaces = parseInt(
      localStorage.getItem("scTimer-decimalPlaces") || "3"
    );

    // Handle different decimal place settings
    if (decimalPlaces === 0) {
      // No decimal places - just return the digits as seconds (no formatting)
      return digits;
    }

    // Always split based on decimal places from the right
    const decimalPart = digits.slice(-decimalPlaces);
    const integerPart = digits.slice(0, -decimalPlaces);

    // If no integer part, it means all digits are decimal
    if (!integerPart) {
      return "0." + decimalPart;
    }

    // Format based on integer part length
    if (integerPart.length <= 2) {
      // Seconds format: SS.XXX
      return integerPart + "." + decimalPart;
    } else if (integerPart.length <= 4) {
      // Minutes format: MM:SS.XXX
      const minutes = integerPart.slice(0, -2);
      const seconds = integerPart.slice(-2);
      return minutes + ":" + seconds + "." + decimalPart;
    } else {
      // Hours format: HH:MM:SS.XXX
      const hours = integerPart.slice(0, -4);
      const minutes = integerPart.slice(-4, -2);
      const seconds = integerPart.slice(-2);
      return hours + ":" + minutes + ":" + seconds + "." + decimalPart;
    }
  }

  // Helper function to get simple ASCII event names for CSV export
  function getSimpleEventName(eventId) {
    const simpleNames = {
      333: "3x3x3",
      222: "2x2x2",
      444: "4x4x4",
      555: "5x5x5",
      666: "6x6x6",
      777: "7x7x7",
      "333bf": "3x3x3 Blindfolded",
      "333fm": "3x3x3 Fewest Moves",
      "333oh": "3x3x3 One-Handed",
      clock: "Clock",
      minx: "Megaminx",
      pyram: "Pyraminx",
      skewb: "Skewb",
      sq1: "Square-1",
      "444bf": "4x4x4 Blindfolded",
      "555bf": "5x5x5 Blindfolded",
      "333mb": "3x3x3 Multi-Blind",
      fto: "Face-Turning Octahedron",
      master_tetraminx: "Master Tetraminx",
      kilominx: "Kilominx",
      redi_cube: "Redi Cube",
      baby_fto: "Baby Face-Turning Octahedron",
    };
    return simpleNames[eventId] || eventId;
  }

  // Function to generate and download share image
  function generateShareImage(
    solve,
    shareType = "normal",
    eventOverride = null,
    eventData = null
  ) {
    try {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");

      // Set canvas size (1080x1080px)
      const size = 1080;
      canvas.width = size;
      canvas.height = size;

      // Check if dark mode is enabled
      const isDarkMode = document.body.classList.contains("dark-mode");

      // Determine template based on share type and dark mode
      let templatePath = "assets/shares/";
      if (shareType === "pb") {
        templatePath += isDarkMode ? "dark_share_pb.png" : "share_pb.png";
      } else if (shareType === "ao5") {
        templatePath += isDarkMode ? "dark_share_bao5.png" : "share_bao5.png";
      } else if (shareType === "ao12") {
        templatePath += isDarkMode ? "dark_share_bao12.png" : "share_bao12.png";
      } else {
        templatePath += isDarkMode ? "dark_share.png" : "share.png";
      }

      // Load the background template
      const templateImg = new Image();
      templateImg.onload = async function () {
        // Draw the template as background
        ctx.drawImage(templateImg, 0, 0, size, size);

        // Set text colors based on dark mode
        const textColor = isDarkMode ? "#ffffff" : "#333333";
        const lowTimeColor = isDarkMode ? "#ff6b6b" : "#e74c3c";
        const highTimeColor = isDarkMode ? "#51cf66" : "#27ae60";

        ctx.fillStyle = textColor;
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";

        // Get event name and session info
        // Use eventOverride if provided, otherwise fall back to currentEvent
        const eventToUse = eventOverride || solve.event || currentEvent;

        // For session name, check if we're in stats modal context
        let sessionName = "";
        if (eventData && eventData.isCustom) {
          // We have custom session data from the clicked box
          sessionName = eventData.name;
        } else if (eventOverride) {
          // We're sharing from stats modal, get the stats session info
          const statsSessionData = getSelectedSessionMetadata();
          sessionName = statsSessionData.selectedSessionId
            ? statsSessionData.selectedSessionName || ""
            : "";
        } else {
          // Regular sharing, use current session
          sessionName = currentSessionId
            ? customSessions.find((s) => s.id === currentSessionId)?.name || ""
            : "";
        }

        // Format event name based on WCA status
        const wcaEvents = [
          "222",
          "333",
          "444",
          "555",
          "666",
          "777",
          "333bf",
          "333fm",
          "333oh",
          "444bf",
          "555bf",
          "clock",
          "minx",
          "pyram",
          "skewb",
          "sq1",
          "333mbf",
          "333ft",
          "333mbo",
          "magic",
          "mmagic",
        ];

        const isWCAEvent = wcaEvents.includes(eventToUse);
        let eventName;

        if (isWCAEvent) {
          // WCA events: just the event name
          eventName = getSimpleEventName(eventToUse);
        } else {
          // Non-WCA events: include session name in parentheses
          const baseEventName = getSimpleEventName(eventToUse);
          if (sessionName) {
            eventName = `${baseEventName} (${sessionName})`;
          } else {
            eventName = baseEventName;
          }
        }

        // Handle different share types
        if (shareType === "ao5" || shareType === "ao12") {
          // Average sharing layout
          drawAverageShareLayout(
            ctx,
            solve,
            shareType,
            size,
            textColor,
            lowTimeColor,
            highTimeColor,
            eventName,
            sessionName,
            isDarkMode,
            isWCAEvent
          );
        } else {
          // Normal or PB sharing layout
          await drawNormalShareLayout(
            ctx,
            solve,
            shareType,
            size,
            textColor,
            eventName,
            sessionName,
            isDarkMode,
            eventToUse,
            isWCAEvent
          );
        }

        // Convert canvas to blob and download
        canvas.toBlob((blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;

            // Generate filename based on share type
            let filename = `scTimer_${eventName}_${shareType}`;
            if (shareType === "normal" || shareType === "pb") {
              const timeText = getTimeText(solve);
              filename += `_${timeText.replace(/[^a-zA-Z0-9]/g, "_")}`;
            }
            filename += `_${new Date().toISOString().split("T")[0]}.png`;

            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
          } else {
            console.error("Failed to create image blob");
          }
        }, "image/png");
      };

      // Set the template image source to load it
      templateImg.src = templatePath;
    } catch (error) {
      console.error("Error generating share image:", error);
    }
  }

  // Helper function to get formatted time text
  function getTimeText(solve) {
    if (solve.isMBLD) {
      return `${solve.score || 0} (${solve.solved || 0}/${solve.total || 0})`;
    } else if (solve.isFMC) {
      if (solve.penalty === "DNF" || solve.isDNF) {
        return "DNF";
      } else {
        return `${solve.moveCount || 0} moves`;
      }
    } else {
      if (solve.penalty === "DNF") {
        return "DNF";
      } else {
        const baseTime =
          solve.time === Infinity ? solve.originalTime || 0 : solve.time;
        let timeText = formatTime(baseTime);
        if (solve.penalty === "+2") {
          timeText += " (+2)";
        }
        return timeText;
      }
    }
  }

  // Helper function to draw normal/PB share layout
  async function drawNormalShareLayout(
    ctx,
    solve,
    shareType,
    size,
    textColor,
    eventName,
    sessionName,
    isDarkMode,
    eventToUse,
    isWCAEvent
  ) {
    let timeText, scrambleText;

    // Check if this is average data (current ao5/ao12 using normal template)
    if (solve.average !== undefined && solve.times) {
      // This is average data
      timeText = formatTime(solve.average);

      // Create times text with brackets for best/worst
      const allTimes = solve.times;
      const validTimes = allTimes.filter((t) => t.time !== Infinity);
      const bestTime = Math.min(...validTimes.map((t) => t.time));
      const worstTime = Math.max(...validTimes.map((t) => t.time));

      scrambleText = allTimes
        .map((solveItem) => {
          const timeStr = formatTime(solveItem.time);
          if (solveItem.time === bestTime) {
            return `(${timeStr})`; // Best time in brackets
          } else if (solveItem.time === worstTime) {
            return `(${timeStr})`; // Worst time in brackets
          } else {
            return timeStr;
          }
        })
        .join(", ");
    } else {
      // This is single solve data
      timeText = getTimeText(solve);

      // Get scramble text
      scrambleText =
        solve.isMBLD && solve.scrambles && solve.scrambles.length > 0
          ? `${solve.scrambles.length} scrambles`
          : solve.scramble || "No scramble";
    }

    // Get date and time
    let dateText, timeOfDay;

    if (solve.average !== undefined && solve.times) {
      // For current averages, use date range like best averages
      const fromDateObj = new Date(solve.fromDate);
      const toDateObj = new Date(solve.toDate);
      const fromDateStr = formatDate(fromDateObj);
      const toDateStr = formatDate(toDateObj);
      const fromTimeStr = fromDateObj.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
      const toTimeStr = toDateObj.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });

      if (fromDateStr === toDateStr) {
        // Same date, show: Date Time to Time
        dateText = fromDateStr;
        timeOfDay = `${fromTimeStr} to ${toTimeStr}`;
      } else {
        // Different dates, show: Date Time to Date Time
        dateText = `${fromDateStr} ${fromTimeStr}`;
        timeOfDay = `to ${toDateStr} ${toTimeStr}`;
      }
    } else {
      // For single solves
      const dateObj = new Date(solve.date || Date.now());
      dateText = formatDate(dateObj);
      timeOfDay = dateObj.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    }

    // 1. Draw scramble in top middle with semi-transparent rounded box (35% from top)
    const scrambleBoxHeight = size * 0.35;
    const scrambleBoxY = 50;
    const scrambleBoxWidth = size - 100;
    const scrambleBoxX = 50;

    // Draw semi-transparent rounded rectangle for scramble
    ctx.save();
    ctx.fillStyle = isDarkMode
      ? "rgba(255, 255, 255, 0.1)"
      : "rgba(0, 0, 0, 0.1)";
    drawRoundedRect(
      ctx,
      scrambleBoxX,
      scrambleBoxY,
      scrambleBoxWidth,
      scrambleBoxHeight,
      20
    );
    ctx.fill();
    ctx.restore();

    // Draw scramble text inside the box with dynamic font sizing
    ctx.fillStyle = textColor;
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";

    const maxScrambleWidth = scrambleBoxWidth - 40;
    const maxScrambleHeight = scrambleBoxHeight - 40;

    // Calculate optimal font size based on text length and box dimensions
    const optimalFontSize = calculateOptimalFontSize(
      ctx,
      scrambleText,
      maxScrambleWidth,
      maxScrambleHeight
    );
    ctx.font = `${optimalFontSize}px monospace`;

    const scrambleLines = wrapText(ctx, scrambleText, maxScrambleWidth);
    const scrambleBoxCenterY = scrambleBoxY + scrambleBoxHeight / 2;
    const lineHeight = optimalFontSize * 1.2;
    const totalTextHeight = scrambleLines.length * lineHeight;
    const startY = scrambleBoxCenterY - totalTextHeight / 2 + lineHeight / 2;

    scrambleLines.forEach((line, index) => {
      ctx.fillText(line, size / 2, startY + index * lineHeight);
    });

    // 2. Draw solve time in center (biggest element)
    ctx.font = "bold 140px Arial";
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    ctx.fillText(timeText, size / 2, size / 2);

    // 3. Draw puzzle icon in bottom right (if not MBLD/FMC and not average data)
    if (!solve.isMBLD && !solve.isFMC && !solve.average) {
      await drawPuzzleIcon(
        ctx,
        eventToUse,
        size - 200,
        size - 200,
        180,
        180,
        isDarkMode
      );
    }

    // 4. Draw bottom left info with exact layout requested
    ctx.font = "24px Arial";
    ctx.textAlign = "left";
    ctx.textBaseline = "top";

    let bottomLeftY = size - 120; // Start higher to accommodate multiple lines

    // Session name (if custom session and WCA event) - first line
    // For non-WCA events, session name is already included in eventName
    if (sessionName && isWCAEvent) {
      ctx.fillText(sessionName, 60, bottomLeftY);
      bottomLeftY += 35;
    }

    // Event name - next line
    ctx.fillText(eventName, 60, bottomLeftY);
    bottomLeftY += 35;

    // Date, time, and scTimer.com - last line
    ctx.fillText(`${dateText} ${timeOfDay} scTimer.com`, 60, bottomLeftY);

    // 5. Add AO5/AO12 indicator for current averages (when using normal template with average data)
    if (solve.average !== undefined && solve.times) {
      const avgType =
        solve.times.length === 5
          ? "AO5"
          : solve.times.length === 12
          ? "AO12"
          : `AO${solve.times.length}`;

      // Draw circle with semi-transparent background (bigger for current averages)
      const circleX = size - 150;
      const circleY = size - 150;
      const circleRadius = 100; // Double the size

      ctx.save();

      // Draw semi-transparent circle background
      ctx.fillStyle = isDarkMode
        ? "rgba(255, 255, 255, 0.2)"
        : "rgba(0, 0, 0, 0.2)";
      ctx.beginPath();
      ctx.arc(circleX, circleY, circleRadius, 0, 2 * Math.PI);
      ctx.fill();

      // Draw circle border
      ctx.strokeStyle = isDarkMode
        ? "rgba(255, 255, 255, 0.4)"
        : "rgba(0, 0, 0, 0.4)";
      ctx.lineWidth = 3;
      ctx.stroke();

      // Draw AO5/AO12 text (bigger font)
      ctx.fillStyle = textColor;
      ctx.font = "bold 48px Arial"; // Even bigger font size
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.fillText(avgType, circleX, circleY);

      ctx.restore();
    }
  }

  // Helper function to draw average share layout
  function drawAverageShareLayout(
    ctx,
    averageData,
    shareType,
    size,
    textColor,
    lowTimeColor,
    highTimeColor,
    eventName,
    sessionName,
    isDarkMode,
    isWCAEvent
  ) {
    // 1. Draw average info in top box (same style as scramble box)
    const infoBoxHeight = size * 0.35;
    const infoBoxY = 50;
    const infoBoxWidth = size - 100;
    const infoBoxX = 50;

    // Draw semi-transparent rounded rectangle
    ctx.save();
    ctx.fillStyle = isDarkMode
      ? "rgba(255, 255, 255, 0.1)"
      : "rgba(0, 0, 0, 0.1)";
    drawRoundedRect(ctx, infoBoxX, infoBoxY, infoBoxWidth, infoBoxHeight, 20);
    ctx.fill();
    ctx.restore();

    // Create times text with lowest and highest in brackets
    const allTimes = averageData.times || [];
    const validTimes = allTimes.filter((t) => t.time !== Infinity);

    if (validTimes.length > 0) {
      const bestTime = Math.min(...validTimes.map((t) => t.time));
      const worstTime = Math.max(...validTimes.map((t) => t.time));

      // Create times text first
      const timesText = allTimes
        .map((solve, index) => {
          const timeStr = formatTime(solve.time);
          return solve.time === bestTime || solve.time === worstTime
            ? `(${timeStr})`
            : timeStr;
        })
        .join(", ");

      // Calculate optimal font size based on text length and box dimensions (same as current averages)
      const maxTimesWidth = infoBoxWidth - 40;
      const maxTimesHeight = infoBoxHeight - 40;
      const optimalTimesSize = calculateOptimalFontSize(
        ctx,
        timesText,
        maxTimesWidth,
        maxTimesHeight
      );
      ctx.font = `${optimalTimesSize}px monospace`;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";

      const infoBoxCenterY = infoBoxY + infoBoxHeight / 2;
      const lineHeight = optimalTimesSize * 1.2;

      // Use the existing wrapText function to handle line breaks properly
      const timesLines = wrapText(ctx, timesText, maxTimesWidth);
      const totalTextHeight = timesLines.length * lineHeight;
      const startY = infoBoxCenterY - totalTextHeight / 2 + lineHeight / 2;

      // Draw the times text normally without individual coloring
      timesLines.forEach((line, lineIndex) => {
        const lineY = startY + lineIndex * lineHeight;

        // Center within the info box, not the entire canvas
        const infoBoxCenterX = infoBoxX + infoBoxWidth / 2;

        // Use normal text color for all times in share images
        ctx.fillStyle = textColor;
        ctx.fillText(line, infoBoxCenterX, lineY);
      });
    }

    // 2. Draw main average time in center
    ctx.fillStyle = textColor;
    ctx.font = "bold 140px Arial";
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    ctx.fillText(formatTime(averageData.average), size / 2, size / 2);

    // 3. No scramble visualization for averages

    // 4. Draw bottom left info with date range
    ctx.font = "24px Arial";
    ctx.textAlign = "left";
    ctx.textBaseline = "top";

    let bottomLeftY = size - 120; // Start higher to accommodate multiple lines

    // Session name (if custom session and WCA event) - first line
    // For non-WCA events, session name is already included in eventName
    if (sessionName && isWCAEvent) {
      ctx.fillText(sessionName, 60, bottomLeftY);
      bottomLeftY += 35;
    }

    // Event name - next line
    ctx.fillText(eventName, 60, bottomLeftY);
    bottomLeftY += 35;

    // Date range and scTimer.com - separate line below event name
    const fromDateObj = new Date(averageData.fromDate);
    const toDateObj = new Date(averageData.toDate);
    const fromDateStr = formatDate(fromDateObj);
    const toDateStr = formatDate(toDateObj);
    const fromTimeStr = fromDateObj.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
    const toTimeStr = toDateObj.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });

    let dateRange;
    if (fromDateStr === toDateStr) {
      // Same date, show: Date Time to Time
      dateRange = `${fromDateStr} ${fromTimeStr} to ${toTimeStr}`;
    } else {
      // Different dates, show: Date Time to Date Time
      dateRange = `${fromDateStr} ${fromTimeStr} to ${toDateStr} ${toTimeStr}`;
    }

    ctx.fillText(`${dateRange} scTimer.com`, 60, bottomLeftY);
  }

  // Helper function to calculate optimal font size for text in a box
  function calculateOptimalFontSize(ctx, text, maxWidth, maxHeight) {
    let fontSize = 50; // Start with a large font size
    let minFontSize = 12; // Minimum readable font size
    let maxFontSize = 50; // Maximum font size

    // Binary search for optimal font size
    while (minFontSize <= maxFontSize) {
      fontSize = Math.floor((minFontSize + maxFontSize) / 2);
      ctx.font = `${fontSize}px monospace`;

      const lines = wrapText(ctx, text, maxWidth);
      const lineHeight = fontSize * 1.2;
      const totalHeight = lines.length * lineHeight;

      // Check if text fits within the box
      const textFits =
        lines.every((line) => ctx.measureText(line).width <= maxWidth) &&
        totalHeight <= maxHeight;

      if (textFits) {
        minFontSize = fontSize + 1;
      } else {
        maxFontSize = fontSize - 1;
      }
    }

    return Math.max(maxFontSize, 12); // Ensure minimum readable size
  }

  // Helper function to draw puzzle icon
  async function drawPuzzleIcon(ctx, eventId, x, y, width, height, isDarkMode) {
    try {
      // Map of WCA events (in assets/svg/event/)
      const wcaEvents = [
        "222",
        "333",
        "444",
        "555",
        "666",
        "777",
        "333bf",
        "333fm",
        "333oh",
        "444bf",
        "555bf",
        "clock",
        "minx",
        "pyram",
        "skewb",
        "sq1",
        "333mbf",
        "333ft",
        "333mbo",
        "magic",
        "mmagic",
      ];

      // Determine if it's a WCA event or unofficial
      const isWCA = wcaEvents.includes(eventId);

      // Map event IDs to actual icon filenames for non-WCA events
      const iconFilenames = {
        master_tetraminx: "mtetram",
        // Add other mappings if needed
      };

      const iconFilename = isWCA ? eventId : iconFilenames[eventId] || eventId;
      const iconPath = isWCA
        ? `assets/svg/event/${iconFilename}.svg`
        : `assets/svg/unofficial/${iconFilename}.svg`;

      // Create an image element to load the SVG
      const img = new Image();

      return new Promise((resolve) => {
        img.onload = function () {
          // Draw the SVG icon
          ctx.save();

          // Apply filter for dark mode if needed
          if (isDarkMode) {
            ctx.filter = "invert(1) brightness(0.8)";
          }

          ctx.drawImage(img, x, y, width, height);
          ctx.restore();
          resolve();
        };

        img.onerror = function () {
          // Fallback if SVG not found
          drawFallbackIcon(ctx, eventId, x, y, width, height, isDarkMode);
          resolve();
        };

        img.src = iconPath;
      });
    } catch (error) {
      console.error("Error loading puzzle icon:", error);
      drawFallbackIcon(ctx, eventId, x, y, width, height, isDarkMode);
    }
  }

  // Fallback icon function
  function drawFallbackIcon(ctx, eventId, x, y, width, height, isDarkMode) {
    ctx.save();

    // Draw background
    ctx.fillStyle = isDarkMode
      ? "rgba(255, 255, 255, 0.1)"
      : "rgba(0, 0, 0, 0.1)";
    drawRoundedRect(ctx, x, y, width, height, 10);
    ctx.fill();

    // Draw border
    ctx.strokeStyle = isDarkMode
      ? "rgba(255, 255, 255, 0.2)"
      : "rgba(0, 0, 0, 0.2)";
    ctx.lineWidth = 1;
    ctx.stroke();

    const centerX = x + width / 2;
    const centerY = y + height / 2;

    // Draw event name
    ctx.fillStyle = isDarkMode ? "#fff" : "#333";
    ctx.font = "bold 24px Arial";
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    ctx.fillText(eventId.toUpperCase(), centerX, centerY);

    ctx.restore();
  }

  // Fallback visualization function
  function drawFallbackVisualization(
    ctx,
    eventId,
    x,
    y,
    width,
    height,
    isDarkMode
  ) {
    ctx.save();

    const centerX = x + width / 2;
    const centerY = y + height / 2;
    const size = Math.min(width, height) * 0.8;

    // Simple colored square/circle based on event
    ctx.fillStyle = isDarkMode ? "#4a5568" : "#e2e8f0";
    ctx.fillRect(x, y, width, height);

    // Event icon/text
    ctx.fillStyle = isDarkMode ? "#fff" : "#333";
    ctx.font = "16px Arial";
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    ctx.fillText(eventId.toUpperCase(), centerX, centerY);

    ctx.restore();
  }

  // Helper function to draw rounded rectangle
  function drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
  }

  // Helper function to wrap text
  function wrapText(ctx, text, maxWidth) {
    const words = text.split(" ");
    const lines = [];
    let currentLine = "";

    for (let i = 0; i < words.length; i++) {
      const testLine = currentLine + (currentLine ? " " : "") + words[i];
      const metrics = ctx.measureText(testLine);

      if (metrics.width > maxWidth && currentLine) {
        lines.push(currentLine);
        currentLine = words[i];
      } else {
        currentLine = testLine;
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  }

  // Function to update overview statistics
  function updateOverviewStats(times) {
    const detailSolveCount = document.getElementById("detail-solve-count");
    const detailBestTime = document.getElementById("detail-best-time");
    const detailWorstTime = document.getElementById("detail-worst-time");
    const detailMeanTime = document.getElementById("detail-mean-time");

    if (detailSolveCount) detailSolveCount.textContent = times.length;

    if (times.length > 0) {
      const validTimes = times.filter((t) => t.time !== Infinity);

      if (validTimes.length > 0) {
        const bestTime = Math.min(...validTimes.map((t) => t.time));
        const worstTime = Math.max(...validTimes.map((t) => t.time));
        const sum = validTimes.reduce((total, t) => total + t.time, 0);
        const mean = sum / validTimes.length;

        if (detailBestTime) {
          detailBestTime.textContent = formatTime(bestTime);
          // Make the entire stat card clickable
          const bestTimeCard = detailBestTime.closest(".stat-card");
          if (bestTimeCard) {
            bestTimeCard.style.cursor = "pointer";
            bestTimeCard.onclick = () => showStatDetailModal("best", times);
          }
        }
        if (detailWorstTime) {
          detailWorstTime.textContent = formatTime(worstTime);
          // Make the entire stat card clickable
          const worstTimeCard = detailWorstTime.closest(".stat-card");
          if (worstTimeCard) {
            worstTimeCard.style.cursor = "pointer";
            worstTimeCard.onclick = () => showStatDetailModal("worst", times);
          }
        }
        if (detailMeanTime) detailMeanTime.textContent = formatTime(mean);
      } else {
        if (detailBestTime) {
          detailBestTime.textContent = "DNF";
          const bestTimeCard = detailBestTime.closest(".stat-card");
          if (bestTimeCard) {
            bestTimeCard.style.cursor = "default";
            bestTimeCard.onclick = null;
          }
        }
        if (detailWorstTime) {
          detailWorstTime.textContent = "DNF";
          const worstTimeCard = detailWorstTime.closest(".stat-card");
          if (worstTimeCard) {
            worstTimeCard.style.cursor = "default";
            worstTimeCard.onclick = null;
          }
        }
        if (detailMeanTime) detailMeanTime.textContent = "DNF";
      }
    } else {
      if (detailBestTime) detailBestTime.textContent = "-";
      if (detailWorstTime) detailWorstTime.textContent = "-";
      if (detailMeanTime) detailMeanTime.textContent = "-";
    }
  }

  // Function to update averages statistics
  function updateAveragesStats(times) {
    const detailAvg5 = document.getElementById("detail-avg5");
    const detailAvg12 = document.getElementById("detail-avg12");
    const detailAvg100 = document.getElementById("detail-avg100");
    const detailAvg1000 = document.getElementById("detail-avg1000");
    const detailStdDev = document.getElementById("detail-std-dev");

    // Calculate averages
    if (detailAvg5) {
      const avg5 = times.length >= 5 ? calculateAverage(times.slice(0, 5)) : 0;
      const avg5Card = detailAvg5.closest(".stat-card");
      if (avg5 === 0) {
        detailAvg5.textContent = "-";
        if (avg5Card) {
          avg5Card.style.cursor = "default";
          avg5Card.onclick = null;
        }
      } else {
        detailAvg5.textContent = formatTime(avg5);
        if (avg5Card) {
          avg5Card.style.cursor = "pointer";
          avg5Card.onclick = () => showStatDetailModal("ao5", times);
        }
      }
    }

    if (detailAvg12) {
      const avg12 =
        times.length >= 12 ? calculateAverage(times.slice(0, 12)) : 0;
      const avg12Card = detailAvg12.closest(".stat-card");
      if (avg12 === 0) {
        detailAvg12.textContent = "-";
        if (avg12Card) {
          avg12Card.style.cursor = "default";
          avg12Card.onclick = null;
        }
      } else {
        detailAvg12.textContent = formatTime(avg12);
        if (avg12Card) {
          avg12Card.style.cursor = "pointer";
          avg12Card.onclick = () => showStatDetailModal("ao12", times);
        }
      }
    }

    if (detailAvg100) {
      const avg100 =
        times.length >= 100 ? calculateAverage(times.slice(0, 100)) : 0;
      const avg100Card = detailAvg100.closest(".stat-card");
      if (avg100 === 0) {
        detailAvg100.textContent = "-";
        if (avg100Card) {
          avg100Card.style.cursor = "default";
          avg100Card.onclick = null;
        }
      } else {
        detailAvg100.textContent = formatTime(avg100);
        if (avg100Card) {
          avg100Card.style.cursor = "pointer";
          avg100Card.onclick = () => showStatDetailModal("ao100", times);
        }
      }
    }

    if (detailAvg1000) {
      const avg1000 =
        times.length >= 1000 ? calculateAverage(times.slice(0, 1000)) : 0;
      const avg1000Card = detailAvg1000.closest(".stat-card");
      if (avg1000 === 0) {
        detailAvg1000.textContent = "-";
        if (avg1000Card) {
          avg1000Card.style.cursor = "default";
          avg1000Card.onclick = null;
        }
      } else {
        detailAvg1000.textContent = formatTime(avg1000);
        if (avg1000Card) {
          avg1000Card.style.cursor = "pointer";
          avg1000Card.onclick = () => showStatDetailModal("ao1000", times);
        }
      }
    }

    // Calculate standard deviation
    if (detailStdDev && times.length > 1) {
      const validTimes = times.filter((t) => t.time !== Infinity);
      if (validTimes.length > 1) {
        const mean =
          validTimes.reduce((sum, t) => sum + t.time, 0) / validTimes.length;
        const variance =
          validTimes.reduce((sum, t) => sum + Math.pow(t.time - mean, 2), 0) /
          validTimes.length;
        const stdDev = Math.sqrt(variance);
        detailStdDev.textContent = formatTime(stdDev);
      } else {
        detailStdDev.textContent = "-";
      }
    } else if (detailStdDev) {
      detailStdDev.textContent = "-";
    }
  }

  // Function to update records statistics
  function updateRecordsStats(times) {
    const recordBestSingle = document.getElementById("record-best-single");
    const recordBestAo5 = document.getElementById("record-best-ao5");
    const recordBestAo12 = document.getElementById("record-best-ao12");
    const recordBestAo100 = document.getElementById("record-best-ao100");
    const recordBestAo1000 = document.getElementById("record-best-ao1000");

    if (times.length > 0) {
      const validTimes = times.filter((t) => t.time !== Infinity);

      // Best single
      if (recordBestSingle && validTimes.length > 0) {
        const bestSingle = Math.min(...validTimes.map((t) => t.time));
        recordBestSingle.textContent = formatTime(bestSingle);
        const bestSingleRecord = recordBestSingle.closest(".record-item");
        if (bestSingleRecord) {
          bestSingleRecord.style.cursor = "pointer";
          bestSingleRecord.onclick = () => showStatDetailModal("best", times);
        }
      } else if (recordBestSingle) {
        recordBestSingle.textContent = "-";
        const bestSingleRecord = recordBestSingle.closest(".record-item");
        if (bestSingleRecord) {
          bestSingleRecord.style.cursor = "default";
          bestSingleRecord.onclick = null;
        }
      }

      // Best ao5
      if (recordBestAo5) {
        let bestAo5 = Infinity;
        let bestAo5Index = -1;
        for (let i = 0; i <= times.length - 5; i++) {
          const avg = calculateAverage(times.slice(i, i + 5));
          if (avg !== 0 && avg < bestAo5) {
            bestAo5 = avg;
            bestAo5Index = i;
          }
        }
        const bestAo5Record = recordBestAo5.closest(".record-item");
        if (bestAo5 === Infinity) {
          recordBestAo5.textContent = "-";
          if (bestAo5Record) {
            bestAo5Record.style.cursor = "default";
            bestAo5Record.onclick = null;
          }
        } else {
          recordBestAo5.textContent = formatTime(bestAo5);
          if (bestAo5Record) {
            bestAo5Record.style.cursor = "pointer";
            bestAo5Record.onclick = () =>
              showStatDetailModal("best-ao5", times, bestAo5Index);
          }
        }
      }

      // Best ao12
      if (recordBestAo12) {
        let bestAo12 = Infinity;
        let bestAo12Index = -1;
        for (let i = 0; i <= times.length - 12; i++) {
          const avg = calculateAverage(times.slice(i, i + 12));
          if (avg !== 0 && avg < bestAo12) {
            bestAo12 = avg;
            bestAo12Index = i;
          }
        }
        const bestAo12Record = recordBestAo12.closest(".record-item");
        if (bestAo12 === Infinity) {
          recordBestAo12.textContent = "-";
          if (bestAo12Record) {
            bestAo12Record.style.cursor = "default";
            bestAo12Record.onclick = null;
          }
        } else {
          recordBestAo12.textContent = formatTime(bestAo12);
          if (bestAo12Record) {
            bestAo12Record.style.cursor = "pointer";
            bestAo12Record.onclick = () =>
              showStatDetailModal("best-ao12", times, bestAo12Index);
          }
        }
      }

      // Best ao100
      if (recordBestAo100) {
        let bestAo100 = Infinity;
        for (let i = 0; i <= times.length - 100; i++) {
          const avg = calculateAverage(times.slice(i, i + 100));
          if (avg !== 0 && avg < bestAo100) {
            bestAo100 = avg;
          }
        }
        recordBestAo100.textContent =
          bestAo100 === Infinity ? "-" : formatTime(bestAo100);
      }

      // Best ao1000
      if (recordBestAo1000) {
        let bestAo1000 = Infinity;
        for (let i = 0; i <= times.length - 1000; i++) {
          const avg = calculateAverage(times.slice(i, i + 1000));
          if (avg !== 0 && avg < bestAo1000) {
            bestAo1000 = avg;
          }
        }
        recordBestAo1000.textContent =
          bestAo1000 === Infinity ? "-" : formatTime(bestAo1000);
      }
    } else {
      if (recordBestSingle) recordBestSingle.textContent = "-";
      if (recordBestAo5) recordBestAo5.textContent = "-";
      if (recordBestAo12) recordBestAo12.textContent = "-";
      if (recordBestAo100) recordBestAo100.textContent = "-";
      if (recordBestAo1000) recordBestAo1000.textContent = "-";
    }
  }

  // Function to update session analysis
  function updateSessionAnalysis(times) {
    const analysisTotalTime = document.getElementById("analysis-total-time");
    const analysisAverageTime = document.getElementById(
      "analysis-average-time"
    );
    const analysisSolvesPerHour = document.getElementById(
      "analysis-solves-per-hour"
    );
    const analysisConsistency = document.getElementById("analysis-consistency");

    if (times.length > 0) {
      const validTimes = times.filter((t) => t.time !== Infinity);

      if (validTimes.length > 0) {
        // Total time
        const totalTime = validTimes.reduce((sum, t) => sum + t.time, 0);
        if (analysisTotalTime) {
          analysisTotalTime.textContent = formatTime(totalTime);
        }

        // Average time
        const averageTime = totalTime / validTimes.length;
        if (analysisAverageTime) {
          analysisAverageTime.textContent = formatTime(averageTime);
        }

        // Solves per hour (estimate based on average time)
        if (analysisSolvesPerHour) {
          const solvesPerHour = Math.round(3600000 / averageTime); // 3600000ms = 1 hour
          analysisSolvesPerHour.textContent = solvesPerHour.toString();
        }

        // Consistency (coefficient of variation)
        if (analysisConsistency && validTimes.length > 1) {
          const mean = averageTime;
          const variance =
            validTimes.reduce((sum, t) => sum + Math.pow(t.time - mean, 2), 0) /
            validTimes.length;
          const stdDev = Math.sqrt(variance);
          const cv = (stdDev / mean) * 100;
          analysisConsistency.textContent = cv.toFixed(1) + "%";
        } else if (analysisConsistency) {
          analysisConsistency.textContent = "-";
        }
      } else {
        if (analysisTotalTime) analysisTotalTime.textContent = "-";
        if (analysisAverageTime) analysisAverageTime.textContent = "-";
        if (analysisSolvesPerHour) analysisSolvesPerHour.textContent = "-";
        if (analysisConsistency) analysisConsistency.textContent = "-";
      }
    } else {
      if (analysisTotalTime) analysisTotalTime.textContent = "-";
      if (analysisAverageTime) analysisAverageTime.textContent = "-";
      if (analysisSolvesPerHour) analysisSolvesPerHour.textContent = "-";
      if (analysisConsistency) analysisConsistency.textContent = "-";
    }
  }

  // Function to update predictions
  function updatePredictions(times) {
    const predictionAo5 = document.getElementById("prediction-ao5");
    const predictionAo12 = document.getElementById("prediction-ao12");
    const predictionImprovement = document.getElementById(
      "prediction-improvement"
    );
    const predictionTarget = document.getElementById("prediction-target");

    if (times.length >= 10) {
      const validTimes = times.filter((t) => t.time !== Infinity);

      if (validTimes.length >= 10) {
        // Calculate trend for improvement rate
        const recentTimes = validTimes.slice(
          0,
          Math.min(20, validTimes.length)
        );
        const olderTimes = validTimes.slice(
          Math.min(20, validTimes.length),
          Math.min(40, validTimes.length)
        );

        if (olderTimes.length > 0) {
          const recentAvg =
            recentTimes.reduce((sum, t) => sum + t.time, 0) /
            recentTimes.length;
          const olderAvg =
            olderTimes.reduce((sum, t) => sum + t.time, 0) / olderTimes.length;
          const improvementRate = ((olderAvg - recentAvg) / olderAvg) * 100;

          if (predictionImprovement) {
            predictionImprovement.textContent =
              improvementRate > 0
                ? `+${improvementRate.toFixed(1)}%`
                : `${improvementRate.toFixed(1)}%`;
          }
        } else if (predictionImprovement) {
          predictionImprovement.textContent = "-";
        }

        // Predict next ao5 target (5% improvement)
        if (predictionAo5 && times.length >= 5) {
          const currentAo5 = calculateAverage(times.slice(0, 5));
          if (currentAo5 !== 0 && currentAo5 !== Infinity) {
            const targetAo5 = currentAo5 * 0.95; // 5% improvement
            predictionAo5.textContent = formatTime(targetAo5);
          } else {
            predictionAo5.textContent = "-";
          }
        } else if (predictionAo5) {
          predictionAo5.textContent = "-";
        }

        // Predict next ao12 target (3% improvement)
        if (predictionAo12 && times.length >= 12) {
          const currentAo12 = calculateAverage(times.slice(0, 12));
          if (currentAo12 !== 0 && currentAo12 !== Infinity) {
            const targetAo12 = currentAo12 * 0.97; // 3% improvement
            predictionAo12.textContent = formatTime(targetAo12);
          } else {
            predictionAo12.textContent = "-";
          }
        } else if (predictionAo12) {
          predictionAo12.textContent = "-";
        }

        // Target time (10% improvement from current average)
        if (predictionTarget) {
          const currentAvg =
            validTimes.reduce((sum, t) => sum + t.time, 0) / validTimes.length;
          const targetTime = currentAvg * 0.9; // 10% improvement
          predictionTarget.textContent = formatTime(targetTime);
        }
      } else {
        if (predictionAo5) predictionAo5.textContent = "-";
        if (predictionAo12) predictionAo12.textContent = "-";
        if (predictionImprovement) predictionImprovement.textContent = "-";
        if (predictionTarget) predictionTarget.textContent = "-";
      }
    } else {
      if (predictionAo5) predictionAo5.textContent = "-";
      if (predictionAo12) predictionAo12.textContent = "-";
      if (predictionImprovement) predictionImprovement.textContent = "-";
      if (predictionTarget) predictionTarget.textContent = "-";
    }
  }

  // Function to update session management buttons
  function updateSessionButtons() {
    const editSessionBtn = document.getElementById("stats-edit-session-btn");
    const emptySessionBtn = document.getElementById("stats-empty-session-btn");

    if (!editSessionBtn || !emptySessionBtn) return;

    // Get selected session data
    const statsEventBtn = document.getElementById("stats-event-selector-btn");
    const selectedEvent = statsEventBtn?.getAttribute("data-event");
    const sessionId = statsEventBtn?.getAttribute("data-session-id");
    const sessionName = statsEventBtn?.getAttribute("data-session-name");

    // Handle "All *" overview options
    if (selectedEvent && selectedEvent.startsWith("all-")) {
      // Always hide edit session button for overview options
      editSessionBtn.style.display = "none";

      // Show empty session button with updated text
      emptySessionBtn.style.display = "inline-flex";
      const emptySessionText = emptySessionBtn.querySelector("span");
      if (emptySessionText) {
        emptySessionText.textContent = "Empty All Sessions";
      }
      return;
    }

    // Reset empty session button text for non-overview options
    const emptySessionText = emptySessionBtn.querySelector("span");
    if (emptySessionText) {
      emptySessionText.setAttribute("data-i18n", "statsDetails.emptySession");
      emptySessionText.textContent = "Empty Session";
    }

    if (sessionId && sessionName) {
      // Show edit session button for custom sessions
      editSessionBtn.style.display = "inline-flex";
      emptySessionBtn.style.display = "none";
    } else {
      // Show empty session button for default session
      editSessionBtn.style.display = "none";
      emptySessionBtn.style.display = "inline-flex";
    }
  }

  // Function to initialize session management buttons
  function initSessionManagementButtons() {
    const editSessionBtn = document.getElementById("stats-edit-session-btn");
    const emptySessionBtn = document.getElementById("stats-empty-session-btn");

    if (editSessionBtn) {
      editSessionBtn.addEventListener("click", () => {
        const statsEventBtn = document.getElementById(
          "stats-event-selector-btn"
        );
        const sessionId = statsEventBtn?.getAttribute("data-session-id");
        const sessionName = statsEventBtn?.getAttribute("data-session-name");
        const eventValue = statsEventBtn?.getAttribute("data-event");

        if (sessionId && sessionName && eventValue) {
          // Temporarily set the current session context for the edit modal
          const originalSessionId = currentSessionId;
          const originalEvent = currentEvent;
          const originalEventText = currentEventText.textContent;

          // Set context for the edit modal
          currentSessionId = sessionId;
          currentEvent = eventValue;
          currentEventText.textContent = sessionName;

          // Close stats panel and open edit session modal
          closeStatsDetails();
          showEditSessionModal();

          // Restore original context after modal is shown
          setTimeout(() => {
            currentSessionId = originalSessionId;
            currentEvent = originalEvent;
            currentEventText.textContent = originalEventText;
          }, 100);
        }
      });
    }

    if (emptySessionBtn) {
      emptySessionBtn.addEventListener("click", () => {
        // Get the selected session data to determine what to clear
        const statsEventBtn = document.getElementById(
          "stats-event-selector-btn"
        );
        const selectedEvent = statsEventBtn?.getAttribute("data-event");
        const sessionId = statsEventBtn?.getAttribute("data-session-id");

        // Use the same logic as the clear times button
        const translations = window.i18nModule?.translations || {};
        const timesTranslations = translations.times || {};
        const modalTranslations = translations.modals || {};

        // Check if this is an "all-*" view
        if (selectedEvent && selectedEvent.startsWith("all-")) {
          handleEmptyAllSessions(selectedEvent, translations);
          return;
        }

        // Single event/session clear
        import("./modal-manager.js").then(({ showConfirm }) => {
          showConfirm(
            timesTranslations.confirmClear ||
              "Are you sure you want to clear all times for this event?",
            function () {
              let storageKey;
              if (sessionId) {
                // Custom session selected in stats modal
                storageKey = `session_${sessionId}`;
              } else if (selectedEvent) {
                // Default event selected in stats modal
                storageKey = selectedEvent;
              } else {
                // Fallback to current context
                storageKey = currentSessionId
                  ? `session_${currentSessionId}`
                  : currentEvent;
              }

              // Clear the times
              timesMap[storageKey] = [];
              saveTimes();

              // Update displays
              updateStatsDetails();
              updateStats();
              updateTimesList();

              // Update timer display to show 0.000 since all times are cleared
              if (timerState === TIMER_STATE.IDLE) {
                timerElement.textContent = formatTime(0);
                updateDisplay(0);
              }
            },
            null,
            modalTranslations.confirm || "Confirm"
          );
        });
      });
    }

    // Function to handle empty all sessions
    function handleEmptyAllSessions(viewType, translations) {
      const timesTranslations = translations.times || {};
      const modalTranslations = translations.modals || {};

      // Define event categories
      const wcaEvents = [
        "333",
        "222",
        "444",
        "555",
        "666",
        "777",
        "333bf",
        "333fm",
        "333oh",
        "clock",
        "minx",
        "pyram",
        "skewb",
        "sq1",
        "444bf",
        "555bf",
        "333mb",
      ];
      const nonWcaEvents = [
        "fto",
        "master_tetraminx",
        "kilominx",
        "redi_cube",
        "baby_fto",
      ];

      let eventsToEmpty = [];
      let confirmMessage = "";

      switch (viewType) {
        case "all-wca":
          eventsToEmpty = [...wcaEvents]; // Include all WCA events
          confirmMessage =
            "Are you sure you want to clear all times for ALL WCA events? This action cannot be undone.";
          break;
        case "all-non-wca":
          eventsToEmpty = [...nonWcaEvents]; // Include all non-WCA events
          // Add custom sessions (all of them, regardless of whether they have times)
          customSessions.forEach((session) => {
            const storageKey = `session_${session.id}`;
            eventsToEmpty.push(storageKey);
          });
          confirmMessage =
            "Are you sure you want to clear all times for ALL Non-WCA events and custom sessions? This action cannot be undone.";
          break;
        case "all-events":
          eventsToEmpty = [...wcaEvents, ...nonWcaEvents]; // Include all events
          // Add custom sessions (all of them, regardless of whether they have times)
          customSessions.forEach((session) => {
            const storageKey = `session_${session.id}`;
            eventsToEmpty.push(storageKey);
          });
          confirmMessage =
            "Are you sure you want to clear ALL times for ALL events and sessions? This action cannot be undone.";
          break;
      }

      if (eventsToEmpty.length === 0) {
        import("./modal-manager.js").then(({ showAlert }) => {
          showAlert("No times to clear in this category.");
        });
        return;
      }

      import("./modal-manager.js").then(({ showConfirm }) => {
        showConfirm(
          confirmMessage,
          function () {
            let clearedEvents = 0;
            let totalClearedTimes = 0;

            // Track which custom sessions to remove
            const customSessionsToRemove = [];

            eventsToEmpty.forEach((eventKey) => {
              const timesCount = timesMap[eventKey]
                ? timesMap[eventKey].length
                : 0;

              // Always clear the event/session, even if it has 0 times
              timesMap[eventKey] = [];
              localStorage.setItem(`scTimer-${eventKey}`, JSON.stringify([]));

              // Count all events/sessions that were processed
              clearedEvents++;
              totalClearedTimes += timesCount;

              // If this is a custom session, mark it for removal
              if (eventKey.startsWith("session_")) {
                const sessionId = eventKey.replace("session_", "");
                customSessionsToRemove.push(sessionId);
              }
            });

            // Remove custom sessions from the array and update localStorage
            if (customSessionsToRemove.length > 0) {
              customSessions = customSessions.filter(
                (session) => !customSessionsToRemove.includes(session.id)
              );
              saveCustomSessions();
              updateCustomSessionsInDropdown();
            }

            // Update displays
            updateStatsDetails();
            updateStats();
            updateTimesList();

            // Update timer display to show 0.000 since all times are cleared
            if (timerState === TIMER_STATE.IDLE) {
              timerElement.textContent = formatTime(0);
              updateDisplay(0);
            }

            // Refresh the all-events view to show updated data
            const statsEventBtn = document.getElementById(
              "stats-event-selector-btn"
            );
            const currentViewType = statsEventBtn?.getAttribute("data-event");
            if (currentViewType && currentViewType.startsWith("all-")) {
              populateAllEventsView(currentViewType);
            }

            // Show success message
            import("./modal-manager.js").then(({ showAlert }) => {
              showAlert(
                `Cleared ${totalClearedTimes} times from ${clearedEvents} events/sessions.`
              );
            });
          },
          null,
          modalTranslations.confirm || "Confirm"
        );
      });
    }
  }

  // Function to update charts
  function updateCharts(times) {
    updateTimeDistributionChart(times);
    updateProgressChart(times);
  }

  // Function to show detailed statistics modal
  function showStatDetailModal(
    statType,
    times,
    startIndex = 0,
    eventData = null
  ) {
    const modal = document.getElementById("stat-detail-modal");
    const title = document.getElementById("stat-detail-title");
    const value = document.getElementById("stat-detail-value");
    const datetime = document.getElementById("stat-detail-datetime");
    const scramble = document.getElementById("stat-detail-scramble");
    const averageInfo = document.getElementById("stat-detail-average-info");
    const timesContainer = document.getElementById("stat-detail-times");
    const scramblesContainer = document.getElementById(
      "stat-detail-scrambles-list"
    );
    const copyButton = document.getElementById("stat-detail-copy-scramble");

    // Average duration elements
    const datetimeRange = document.getElementById("stat-detail-datetime-range");
    const singleScrambleSection = document.getElementById(
      "stat-detail-single-scramble-section"
    );

    // Elements for dynamic switching
    const datetimeItem = document.getElementById("stat-detail-datetime-item");
    const datetimeLabel = document.getElementById("stat-detail-datetime-label");

    // Share button
    const shareButton = document.getElementById("stat-detail-share");

    if (!modal) return;

    // Get translations
    const translations = window.i18nModule?.translations || {};
    const statsTranslations = translations.stats || {};

    // Get event information for header - use eventData if available (for all* options)
    let headerEvent, headerSessionName, statsEvent;

    if (eventData) {
      // We have event data from the clicked box (all* options)
      if (eventData.isCustom) {
        // Custom session
        headerEvent = eventData.puzzleType;
        headerSessionName = eventData.name;
      } else {
        // Regular event
        headerEvent = eventData;
        headerSessionName = null;
      }
      statsEvent = headerEvent; // For share button logic
    } else {
      // Use stats modal selection (regular options)
      const sessionData = getSelectedSessionMetadata();
      headerEvent = sessionData.selectedEvent;
      headerSessionName = sessionData.selectedSessionId
        ? sessionData.selectedSessionName
        : null;
      statsEvent = sessionData.selectedEvent;
    }

    // Create event context string for headers
    let eventContext = "";
    if (headerSessionName) {
      // Custom session: "in 3×3×3 (Session Name)"
      eventContext = ` in ${getSimpleEventName(
        headerEvent
      )} (${headerSessionName})`;
    } else {
      // Regular event: "in 3×3×3"
      eventContext = ` in ${getSimpleEventName(headerEvent)}`;
    }

    let statData = null;
    let isAverage = false;

    switch (statType) {
      case "best":
        const validTimes = times.filter((t) => t.time !== Infinity);
        if (validTimes.length > 0) {
          statData = validTimes.reduce((best, current) =>
            current.time < best.time ? current : best
          );
          title.textContent = `${
            statsTranslations.best || "Best"
          } Single${eventContext}`;
        }
        break;

      case "worst":
        const validWorstTimes = times.filter((t) => t.time !== Infinity);
        if (validWorstTimes.length > 0) {
          statData = validWorstTimes.reduce((worst, current) =>
            current.time > worst.time ? current : worst
          );
          title.textContent = `${
            statsTranslations.worst || "Worst"
          } Single${eventContext}`;
        }
        break;

      case "ao5":
        if (times.length >= 5) {
          const avgTimes = times.slice(0, 5);
          const avgValue = calculateAverage(avgTimes);
          if (avgValue !== 0) {
            isAverage = true;
            title.textContent = `Current ${
              statsTranslations.avg5 || "ao5"
            }${eventContext}`;
            value.textContent = formatTime(avgValue);
            showAverageDetails(
              avgTimes,
              scramblesContainer,
              datetimeRange,
              datetime
            );
          }
        }
        break;

      case "ao12":
        if (times.length >= 12) {
          const avgTimes = times.slice(0, 12);
          const avgValue = calculateAverage(avgTimes);
          if (avgValue !== 0) {
            isAverage = true;
            title.textContent = `Current ${
              statsTranslations.avg12 || "ao12"
            }${eventContext}`;
            value.textContent = formatTime(avgValue);
            showAverageDetails(
              avgTimes,
              scramblesContainer,
              datetimeRange,
              datetime
            );
          }
        }
        break;

      case "ao100":
        if (times.length >= 100) {
          const avgTimes = times.slice(0, 100);
          const avgValue = calculateAverage(avgTimes);
          if (avgValue !== 0) {
            isAverage = true;
            title.textContent = `Current ${
              statsTranslations.avg100 || "ao100"
            }${eventContext}`;
            value.textContent = formatTime(avgValue);
            showAverageDetails(
              avgTimes,
              scramblesContainer,
              datetimeRange,
              datetime
            );
          }
        }
        break;

      case "ao1000":
        if (times.length >= 1000) {
          const avgTimes = times.slice(0, 1000);
          const avgValue = calculateAverage(avgTimes);
          if (avgValue !== 0) {
            isAverage = true;
            title.textContent = `Current ${
              statsTranslations.avg1000 || "ao1000"
            }`;
            value.textContent = formatTime(avgValue);
            showAverageDetails(
              avgTimes,
              scramblesContainer,
              datetimeRange,
              datetime
            );
          }
        }
        break;

      case "best-ao5":
        if (times.length >= 5 && startIndex >= 0) {
          const avgTimes = times.slice(startIndex, startIndex + 5);
          const avgValue = calculateAverage(avgTimes);
          if (avgValue !== 0) {
            isAverage = true;
            title.textContent = `Best ${
              statsTranslations.avg5 || "ao5"
            }${eventContext}`;
            value.textContent = formatTime(avgValue);
            showAverageDetails(
              avgTimes,
              scramblesContainer,
              datetimeRange,
              datetime
            );
          }
        }
        break;

      case "best-ao12":
        if (times.length >= 12 && startIndex >= 0) {
          const avgTimes = times.slice(startIndex, startIndex + 12);
          const avgValue = calculateAverage(avgTimes);
          if (avgValue !== 0) {
            isAverage = true;
            title.textContent = `Best ${
              statsTranslations.avg12 || "ao12"
            }${eventContext}`;
            value.textContent = formatTime(avgValue);
            showAverageDetails(
              avgTimes,
              scramblesContainer,
              datetimeRange,
              datetime
            );
          }
        }
        break;
    }

    if (statData && !isAverage) {
      // Show single solve details
      if (value) value.textContent = formatTime(statData.time);

      // Set the second field to show Date & Time for single solves
      if (datetimeLabel) datetimeLabel.textContent = "Date & Time:";
      if (datetime) {
        const solveDate = new Date(statData.date);
        datetime.textContent = `${formatDate(
          solveDate
        )} ${solveDate.toLocaleTimeString()}`;
      }

      if (scramble)
        scramble.textContent = statData.scramble || "No scramble available";

      // Set up copy button
      if (copyButton) {
        copyButton.onclick = () => {
          navigator.clipboard.writeText(statData.scramble || "");

          // Show tick icon temporarily
          const originalIcon = copyButton.innerHTML;
          copyButton.innerHTML = '<i class="fas fa-check"></i>';
          copyButton.style.color = "var(--accent-color)";

          setTimeout(() => {
            copyButton.innerHTML = originalIcon;
            copyButton.style.color = "";
          }, 1500);

          // Show feedback
          const translations = window.i18nModule?.translations || {};
          const gestureTranslations = translations.gestures || {};
          showGestureFeedback(
            gestureTranslations.scrambleCopied || "Scramble copied"
          );
        };
      }

      if (averageInfo) averageInfo.style.display = "none";
      if (singleScrambleSection) singleScrambleSection.style.display = "block";
    } else if (isAverage) {
      // Show average details - set the second field to show Duration
      if (datetimeLabel) datetimeLabel.textContent = "Duration:";
      if (singleScrambleSection) singleScrambleSection.style.display = "none";
      if (averageInfo) averageInfo.style.display = "block";
    }

    // Setup share button
    if (shareButton) {
      // Show share button for shareable stats
      if (
        statType === "best" ||
        statType === "ao5" ||
        statType === "ao12" ||
        statType === "best-ao5" ||
        statType === "best-ao12"
      ) {
        shareButton.style.display = "block";

        // Remove any existing event listeners
        shareButton.replaceWith(shareButton.cloneNode(true));
        const newShareButton = document.getElementById("stat-detail-share");

        newShareButton.addEventListener("click", () => {
          let shareType = "normal";
          let shareData = null;

          if (statType === "best") {
            shareType = "pb";
            shareData = statData;
          } else if (statType === "ao5" || statType === "best-ao5") {
            // For current averages, use normal template without visualization
            shareType = statType === "ao5" ? "normal" : "ao5";

            // Create average data for sharing
            const avgTimes =
              statType === "ao5"
                ? times.slice(0, 5)
                : times.slice(startIndex, startIndex + 5);
            const avgValue = calculateAverage(avgTimes);
            shareData = {
              average: avgValue,
              times: avgTimes, // Include all times for display
              best: Math.min(
                ...avgTimes
                  .filter((t) => t.time !== Infinity)
                  .map((t) => t.time)
              ),
              worst: Math.max(
                ...avgTimes
                  .filter((t) => t.time !== Infinity)
                  .map((t) => t.time)
              ),
              fromDate: avgTimes[avgTimes.length - 1].date,
              toDate: avgTimes[0].date,
            };
          } else if (statType === "ao12" || statType === "best-ao12") {
            // For current averages, use normal template without visualization
            shareType = statType === "ao12" ? "normal" : "ao12";

            // Create average data for sharing
            const avgTimes =
              statType === "ao12"
                ? times.slice(0, 12)
                : times.slice(startIndex, startIndex + 12);
            const avgValue = calculateAverage(avgTimes);
            shareData = {
              average: avgValue,
              times: avgTimes, // Include all times for display
              best: Math.min(
                ...avgTimes
                  .filter((t) => t.time !== Infinity)
                  .map((t) => t.time)
              ),
              worst: Math.max(
                ...avgTimes
                  .filter((t) => t.time !== Infinity)
                  .map((t) => t.time)
              ),
              fromDate: avgTimes[avgTimes.length - 1].date,
              toDate: avgTimes[0].date,
            };
          }

          if (shareData) {
            // Get event from the clicked box if available (for all* options)
            let eventToShare = statsEvent; // Default to stats modal selection

            if (eventData) {
              // We have event data from the clicked box
              if (eventData.isCustom) {
                // Custom session: use the puzzle type
                eventToShare = eventData.puzzleType;
              } else {
                // Regular event: use the event ID
                eventToShare = eventData;
              }
            } else {
              // Fallback: check if this is an all* option
              const statsEventText =
                document.getElementById("stats-event-text");
              if (
                statsEventText &&
                statsEventText.textContent.includes("all*")
              ) {
                // This is an all* option, get event from the solve data
                if (statData && statData.event) {
                  // For single solves, use the event from the solve
                  eventToShare = statData.event;
                } else if (times && times.length > 0 && times[0].event) {
                  // For averages, use the event from the first solve in the average
                  eventToShare = times[0].event;
                }
              }
            }

            generateShareImage(shareData, shareType, eventToShare, eventData);
          }
        });
      } else {
        shareButton.style.display = "none";
      }
    }

    // Show modal
    modal.classList.add("show");

    // Check if modal content is scrollable and add scroll indicator
    setTimeout(() => {
      const modalContent = modal.querySelector(".modal-content");
      const modalBody = modal.querySelector(".modal-body");
      if (modalContent && modalBody) {
        if (modalBody.scrollHeight > modalBody.clientHeight) {
          modalContent.classList.add("has-scroll");
        } else {
          modalContent.classList.remove("has-scroll");
        }

        // Update scroll indicator on scroll
        modalBody.addEventListener("scroll", () => {
          const isAtBottom =
            modalBody.scrollTop + modalBody.clientHeight >=
            modalBody.scrollHeight - 5;
          if (isAtBottom) {
            modalContent.classList.remove("has-scroll");
          } else {
            modalContent.classList.add("has-scroll");
          }
        });
      }
    }, 100);

    // Set up close handlers
    const closeButton = document.getElementById("stat-detail-close");
    const closeModal = () => {
      modal.classList.remove("show");
      copyButton.style.display = "inline-block"; // Reset copy button
    };

    closeButton.onclick = closeModal;
    modal.onclick = (e) => {
      if (e.target === modal) closeModal();
    };
  }

  // Helper function to show average details
  function showAverageDetails(
    avgTimes,
    scramblesContainer,
    datetimeRange,
    durationField
  ) {
    // Clear the scrambles container
    if (scramblesContainer) {
      scramblesContainer.innerHTML = "";
    }

    // Calculate which times are best/worst (excluded from average)
    const sortedTimes = [...avgTimes].sort((a, b) => a.time - b.time);
    const bestTime = sortedTimes[0].time;
    const worstTime = sortedTimes[sortedTimes.length - 1].time;

    // Calculate duration info
    if (avgTimes.length > 0) {
      const firstSolve = avgTimes[avgTimes.length - 1]; // Last in the array is first chronologically
      const lastSolve = avgTimes[0]; // First in the array is most recent

      const firstDate = new Date(firstSolve.date);
      const lastDate = new Date(lastSolve.date);

      // Set combined datetime range
      if (datetimeRange) {
        const firstDateStr = formatDate(firstDate);
        const firstTimeStr = firstDate.toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        });
        const lastDateStr = formatDate(lastDate);
        const lastTimeStr = lastDate.toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        });

        // If same date, merge them: "Date Time to Time"
        if (firstDateStr === lastDateStr) {
          datetimeRange.textContent = `${firstDateStr} ${firstTimeStr} to ${lastTimeStr}`;
        } else {
          // Different dates: "Date Time to Date Time"
          datetimeRange.textContent = `${firstDateStr} ${firstTimeStr} to ${lastDateStr} ${lastTimeStr}`;
        }
      }

      // Calculate and set duration with improved formatting in the main datetime field
      if (durationField) {
        const durationMs = lastDate.getTime() - firstDate.getTime();
        const durationMinutes = Math.round(durationMs / (1000 * 60));
        const durationHours = Math.round(durationMs / (1000 * 60 * 60));
        const durationDays = Math.round(durationMs / (1000 * 60 * 60 * 24));

        if (durationMs < 60000) {
          // Less than 1 minute
          durationField.textContent = "< 1 minute";
        } else if (durationMinutes < 60) {
          // Less than 1 hour
          durationField.textContent =
            durationMinutes === 1 ? "1 minute" : `${durationMinutes} minutes`;
        } else if (durationHours < 24) {
          // Less than 1 day
          durationField.textContent =
            durationHours === 1 ? "1 hour" : `${durationHours} hours`;
        } else {
          // 1 day or more
          durationField.textContent =
            durationDays === 1 ? "1 day" : `${durationDays} days`;
        }
      }
    }

    // Create scramble and time pairs
    if (scramblesContainer) {
      avgTimes.forEach((solve) => {
        const scrambleItem = document.createElement("div");
        scrambleItem.className = "stat-detail-scramble-item";

        const scrambleText = document.createElement("div");
        scrambleText.className = "stat-detail-scramble-text";
        scrambleText.textContent = solve.scramble || "No scramble available";

        const timeText = document.createElement("div");
        timeText.className = "stat-detail-scramble-time";

        if (solve.time === bestTime) {
          timeText.classList.add("best");
          timeText.textContent = `(${formatTime(solve.time)})`;
        } else if (solve.time === worstTime) {
          timeText.classList.add("worst");
          timeText.textContent = `(${formatTime(solve.time)})`;
        } else {
          timeText.textContent = formatTime(solve.time);
        }

        scrambleItem.appendChild(scrambleText);
        scrambleItem.appendChild(timeText);
        scramblesContainer.appendChild(scrambleItem);
      });
    }
  }

  // Function to update time distribution chart
  function updateTimeDistributionChart(times) {
    const canvas = document.getElementById("time-distribution-chart");
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    const validTimes = times.filter((t) => t.time !== Infinity);

    if (validTimes.length === 0) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = "#666";
      ctx.font = "16px Arial";
      ctx.textAlign = "center";
      ctx.fillText("No data available", canvas.width / 2, canvas.height / 2);
      return;
    }

    // Create histogram
    const timeValues = validTimes.map((t) => t.time);
    const minTime = Math.min(...timeValues);
    const maxTime = Math.max(...timeValues);
    const range = maxTime - minTime;
    const bucketCount = Math.min(
      10,
      Math.max(5, Math.floor(validTimes.length / 5))
    );
    const bucketSize = range / bucketCount;

    const buckets = new Array(bucketCount).fill(0);
    timeValues.forEach((time) => {
      const bucketIndex = Math.min(
        bucketCount - 1,
        Math.floor((time - minTime) / bucketSize)
      );
      buckets[bucketIndex]++;
    });

    // Draw chart
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    const maxCount = Math.max(...buckets, 1); // Ensure at least 1 to avoid division by zero
    const barWidth = (canvas.width - 60) / bucketCount; // Leave space for y-axis
    const maxBarHeight = canvas.height - 60; // Leave space for x-axis
    const chartStartX = 40;
    const chartStartY = 20;

    // Draw grid lines
    ctx.strokeStyle =
      getComputedStyle(document.documentElement).getPropertyValue(
        "--border-color"
      ) || "#ddd";
    ctx.lineWidth = 0.5;

    // Horizontal grid lines
    for (let i = 0; i <= 5; i++) {
      const y = chartStartY + (i * maxBarHeight) / 5;
      ctx.beginPath();
      ctx.moveTo(chartStartX, y);
      ctx.lineTo(chartStartX + barWidth * bucketCount, y);
      ctx.stroke();
    }

    // Vertical grid lines
    for (let i = 0; i <= bucketCount; i++) {
      const x = chartStartX + i * barWidth;
      ctx.beginPath();
      ctx.moveTo(x, chartStartY);
      ctx.lineTo(x, chartStartY + maxBarHeight);
      ctx.stroke();
    }

    // Draw bars
    ctx.fillStyle =
      getComputedStyle(document.documentElement).getPropertyValue(
        "--accent-color"
      ) || "#a57865";

    buckets.forEach((count, index) => {
      const barHeight = (count / maxCount) * maxBarHeight;
      const x = chartStartX + index * barWidth;
      const y = chartStartY + maxBarHeight - barHeight;

      // Only draw if barHeight is valid
      if (isFinite(barHeight) && barHeight > 0) {
        // Draw bar with gradient
        const gradient = ctx.createLinearGradient(0, y, 0, y + barHeight);
        gradient.addColorStop(0, ctx.fillStyle);
        gradient.addColorStop(1, ctx.fillStyle + "80"); // Add transparency
        ctx.fillStyle = gradient;

        ctx.fillRect(x + 2, y, barWidth - 4, barHeight);
      }

      // Draw count on top of bar
      if (count > 0) {
        ctx.fillStyle =
          getComputedStyle(document.documentElement).getPropertyValue(
            "--text-color"
          ) || "#333";
        ctx.font = "10px Arial";
        ctx.textAlign = "center";
        ctx.fillText(count.toString(), x + barWidth / 2, y - 5);
      }

      // Reset fill style for next bar
      ctx.fillStyle =
        getComputedStyle(document.documentElement).getPropertyValue(
          "--accent-color"
        ) || "#a57865";
    });

    // Draw x-axis labels
    ctx.fillStyle =
      getComputedStyle(document.documentElement).getPropertyValue(
        "--text-color"
      ) || "#666";
    ctx.font = "10px Arial";
    ctx.textAlign = "center";
    buckets.forEach((count, index) => {
      const bucketStart = minTime + index * bucketSize;
      const bucketEnd = minTime + (index + 1) * bucketSize;
      const label = `${formatTime(bucketStart).substring(0, 5)}-${formatTime(
        bucketEnd
      ).substring(0, 5)}`;
      const x = chartStartX + index * barWidth + barWidth / 2;
      ctx.save();
      ctx.translate(x, canvas.height - 5);
      ctx.rotate(-Math.PI / 4); // Rotate labels for better readability
      ctx.fillText(label, 0, 0);
      ctx.restore();
    });

    // Draw y-axis labels
    ctx.textAlign = "right";
    for (let i = 0; i <= 5; i++) {
      const value = Math.round((maxCount * (5 - i)) / 5);
      const y = chartStartY + (i * maxBarHeight) / 5 + 3;
      ctx.fillText(value.toString(), chartStartX - 5, y);
    }

    // Draw axis titles
    ctx.font = "12px Arial";
    ctx.textAlign = "center";
    ctx.fillText("Time Range", canvas.width / 2, canvas.height - 5);

    ctx.save();
    ctx.translate(15, canvas.height / 2);
    ctx.rotate(-Math.PI / 2);
    ctx.fillText("Frequency", 0, 0);
    ctx.restore();
  }

  // Function to update progress chart
  function updateProgressChart(times) {
    const canvas = document.getElementById("progress-chart");
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    const validTimes = times.filter((t) => t.time !== Infinity);

    if (validTimes.length < 2) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = "#666";
      ctx.font = "16px Arial";
      ctx.textAlign = "center";
      ctx.fillText("Need more data", canvas.width / 2, canvas.height / 2);
      return;
    }

    // Calculate rolling averages
    const rollingAverages = [];
    const windowSize = Math.min(5, validTimes.length);

    for (let i = 0; i <= validTimes.length - windowSize; i++) {
      const window = validTimes.slice(i, i + windowSize);
      const avg = window.reduce((sum, t) => sum + t.time, 0) / window.length;
      rollingAverages.push(avg);
    }

    if (rollingAverages.length === 0) return;

    // Draw chart
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    const minTime = Math.min(...rollingAverages);
    const maxTime = Math.max(...rollingAverages);
    const range = maxTime - minTime;
    const padding = range * 0.1;

    const chartMinTime = minTime - padding;
    const chartMaxTime = maxTime + padding;
    const chartRange = chartMaxTime - chartMinTime;

    const chartStartX = 50;
    const chartStartY = 20;
    const chartWidth = canvas.width - 70;
    const chartHeight = canvas.height - 60;

    // Draw grid lines
    ctx.strokeStyle =
      getComputedStyle(document.documentElement).getPropertyValue(
        "--border-color"
      ) || "#ddd";
    ctx.lineWidth = 0.5;

    // Horizontal grid lines
    for (let i = 0; i <= 5; i++) {
      const y = chartStartY + (i * chartHeight) / 5;
      ctx.beginPath();
      ctx.moveTo(chartStartX, y);
      ctx.lineTo(chartStartX + chartWidth, y);
      ctx.stroke();
    }

    // Vertical grid lines
    const gridCount = Math.min(10, rollingAverages.length);
    for (let i = 0; i <= gridCount; i++) {
      const x = chartStartX + (i * chartWidth) / gridCount;
      ctx.beginPath();
      ctx.moveTo(x, chartStartY);
      ctx.lineTo(x, chartStartY + chartHeight);
      ctx.stroke();
    }

    // Calculate step for x-axis
    const stepX = chartWidth / (rollingAverages.length - 1);

    // Draw trend line (linear regression)
    if (rollingAverages.length > 2) {
      const n = rollingAverages.length;
      let sumX = 0,
        sumY = 0,
        sumXY = 0,
        sumXX = 0;

      rollingAverages.forEach((avg, index) => {
        sumX += index;
        sumY += avg;
        sumXY += index * avg;
        sumXX += index * index;
      });

      const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
      const intercept = (sumY - slope * sumX) / n;

      // Draw trend line
      ctx.strokeStyle =
        getComputedStyle(document.documentElement).getPropertyValue(
          "--text-color"
        ) || "#666";
      ctx.lineWidth = 1;
      ctx.setLineDash([5, 5]);
      ctx.beginPath();

      const startTrendY =
        chartStartY +
        chartHeight -
        ((intercept - chartMinTime) / chartRange) * chartHeight;
      const endTrendY =
        chartStartY +
        chartHeight -
        ((slope * (n - 1) + intercept - chartMinTime) / chartRange) *
          chartHeight;

      ctx.moveTo(chartStartX, startTrendY);
      ctx.lineTo(chartStartX + chartWidth, endTrendY);
      ctx.stroke();
      ctx.setLineDash([]); // Reset dash
    }

    // Draw main line
    ctx.strokeStyle =
      getComputedStyle(document.documentElement).getPropertyValue(
        "--accent-color"
      ) || "#a57865";
    ctx.lineWidth = 3;
    ctx.beginPath();

    rollingAverages.forEach((avg, index) => {
      const x = chartStartX + index * stepX;
      const y =
        chartStartY +
        chartHeight -
        ((avg - chartMinTime) / chartRange) * chartHeight;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();

    // Draw points with hover effect
    ctx.fillStyle =
      getComputedStyle(document.documentElement).getPropertyValue(
        "--accent-color"
      ) || "#a57865";

    rollingAverages.forEach((avg, index) => {
      const x = chartStartX + index * stepX;
      const y =
        chartStartY +
        chartHeight -
        ((avg - chartMinTime) / chartRange) * chartHeight;

      // Draw point
      ctx.beginPath();
      ctx.arc(x, y, 4, 0, 2 * Math.PI);
      ctx.fill();

      // Draw white border
      ctx.strokeStyle =
        getComputedStyle(document.documentElement).getPropertyValue(
          "--container-bg"
        ) || "#fff";
      ctx.lineWidth = 2;
      ctx.stroke();
    });

    // Draw y-axis labels
    ctx.fillStyle =
      getComputedStyle(document.documentElement).getPropertyValue(
        "--text-color"
      ) || "#666";
    ctx.font = "10px Arial";
    ctx.textAlign = "right";
    for (let i = 0; i <= 5; i++) {
      const value = chartMaxTime - (i * chartRange) / 5;
      const y = chartStartY + (i * chartHeight) / 5 + 3;
      ctx.fillText(formatTime(value).substring(0, 6), chartStartX - 5, y);
    }

    // Draw x-axis labels (solve numbers)
    ctx.textAlign = "center";
    const labelStep = Math.max(1, Math.floor(rollingAverages.length / 8));
    for (let i = 0; i < rollingAverages.length; i += labelStep) {
      const x = chartStartX + i * stepX;
      const solveNumber = validTimes.length - windowSize - i + 1;
      ctx.fillText(solveNumber.toString(), x, canvas.height - 5);
    }

    // Draw axis titles
    ctx.font = "12px Arial";
    ctx.textAlign = "center";
    ctx.fillText("Solve Number", canvas.width / 2, canvas.height - 5);

    ctx.save();
    ctx.translate(15, canvas.height / 2);
    ctx.rotate(-Math.PI / 2);
    ctx.fillText("Time", 0, 0);
    ctx.restore();

    // Add legend
    ctx.font = "10px Arial";
    ctx.textAlign = "left";
    ctx.fillStyle =
      getComputedStyle(document.documentElement).getPropertyValue(
        "--accent-color"
      ) || "#a57865";
    ctx.fillText("● Rolling Average", chartStartX, chartStartY - 5);

    if (rollingAverages.length > 2) {
      ctx.fillStyle =
        getComputedStyle(document.documentElement).getPropertyValue(
          "--text-color"
        ) || "#666";
      ctx.fillText("--- Trend", chartStartX + 120, chartStartY - 5);
    }
  }

  // Function to detect file format
  function detectFileFormat(content, filename) {
    if (filename.endsWith(".json")) {
      try {
        const data = JSON.parse(content);
        if (data.event || data.events || data.times) {
          return { format: "scTimer_JSON", data: data };
        }
      } catch (e) {
        return { format: "unknown" };
      }
    } else if (filename.endsWith(".csv")) {
      const lines = content.split("\n").filter((line) => line.trim());
      if (lines.length < 2) return { format: "unknown" };

      const firstLine = lines[0].toLowerCase();

      // Check for cstimer format (semicolon-delimited with specific headers)
      if (firstLine.includes("no.;time;comment;scramble;date;p.1")) {
        return { format: "cstimer_CSV" };
      }

      // Check for scTimer format (comma-delimited with specific headers)
      if (
        firstLine.includes("solve number") &&
        firstLine.includes("time (seconds)") &&
        firstLine.includes("penalty") &&
        firstLine.includes("comment")
      ) {
        return { format: "scTimer_CSV" };
      }

      // Fallback: detect by delimiter and content
      const semicolonCount = firstLine.split(";").length - 1;
      const commaCount = firstLine.split(",").length - 1;

      if (
        semicolonCount > commaCount &&
        (firstLine.includes("no.") || firstLine.includes("p.1"))
      ) {
        return { format: "cstimer_CSV" };
      } else if (commaCount > semicolonCount) {
        return { format: "scTimer_CSV" };
      }
    }

    return { format: "plain_text" };
  }

  // Function to parse cstimer CSV format
  function parseCstimerCSV(content) {
    const lines = content.split("\n").filter((line) => line.trim());
    const importedTimes = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(";");

      if (values.length >= 6 && values[1]) {
        const timeStr = values[1].trim();
        const comment = values[2] ? values[2].trim() : null;
        const scramble = values[3] ? values[3].trim() : "";
        const dateStr = values[4] ? values[4].trim() : "";

        // Parse time - handle DNF, +2, and regular times
        let timeValue = 0;
        let penalty = null;

        if (timeStr.toUpperCase().startsWith("DNF")) {
          // DNF format: "DNF(18.432)" or just "DNF"
          const dnfMatch = timeStr.match(/DNF\(([0-9.]+)\)/i);
          if (dnfMatch) {
            timeValue = parseFloat(dnfMatch[1]) * 1000; // Convert to milliseconds
          } else {
            timeValue = Infinity;
          }
          penalty = "DNF";
        } else if (timeStr.endsWith("+")) {
          // +2 penalty format: "19.773+" - use the displayed time (which includes penalty)
          timeValue = parseFloat(timeStr.slice(0, -1)) * 1000; // Use displayed time
          penalty = "+2";
        } else {
          // Regular time - convert from seconds to milliseconds
          timeValue = parseFloat(timeStr) * 1000;
        }

        // Parse date - cstimer format: "2025-06-10 17:37:28"
        let date = new Date().toISOString();
        if (dateStr) {
          try {
            const parsedDate = new Date(dateStr.replace(" ", "T") + "Z");
            if (!isNaN(parsedDate.getTime())) {
              date = parsedDate.toISOString();
            }
          } catch (e) {
            // Use current date if parsing fails
          }
        }

        if (!isNaN(timeValue) && timeValue > 0) {
          importedTimes.push({
            time: timeValue,
            date: date,
            scramble: scramble,
            penalty: penalty,
            comment: comment || null,
          });
        }
      }
    }

    return importedTimes;
  }

  // Function to parse scTimer CSV format
  function parseScTimerCSV(content) {
    const lines = content.split("\n").filter((line) => line.trim());

    if (lines.length < 2) return [];

    // Check if this is a multi-event CSV (has Event or Session columns)
    const firstLine = lines[0].toLowerCase();
    if (firstLine.includes("event") || firstLine.includes("session")) {
      return parseMultiEventCSV(content);
    }

    // Single event CSV parsing
    const importedTimes = [];

    // Parse headers
    const headers = lines[0].split(",").map((h) => h.toLowerCase().trim());
    const timeSecondsIndex = headers.findIndex((h) =>
      h.includes("time (seconds)")
    );
    const penaltyIndex = headers.findIndex((h) => h.includes("penalty"));
    const commentIndex = headers.findIndex((h) => h.includes("comment"));
    const scrambleIndex = headers.findIndex((h) => h.includes("scramble"));
    const dateIndex = headers.findIndex((h) => h.includes("date"));
    const timeColumnIndex = headers.findIndex((h) => h === "time");

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i];
      const values = parseCSVLine(line); // Handle quoted values properly

      if (values.length > timeSecondsIndex) {
        let timeValue = 0;
        let penalty = null;

        // Get penalty first
        if (penaltyIndex >= 0 && values[penaltyIndex]) {
          const penaltyStr = values[penaltyIndex].trim();
          if (penaltyStr === "DNF") {
            penalty = "DNF";
          } else if (penaltyStr === "+2") {
            penalty = "+2";
          }
        }

        // Get time value from seconds column
        if (timeSecondsIndex >= 0 && values[timeSecondsIndex]) {
          const timeStr = values[timeSecondsIndex].trim();
          if (timeStr === "DNF") {
            timeValue = Infinity;
          } else {
            timeValue = parseFloat(timeStr) * 1000; // Convert seconds to milliseconds
          }
        }

        // Parse date and time
        let date = new Date().toISOString();
        if (
          dateIndex >= 0 &&
          values[dateIndex] &&
          timeColumnIndex >= 0 &&
          values[timeColumnIndex]
        ) {
          try {
            const dateStr = values[dateIndex].trim();
            const timeStr = values[timeColumnIndex].trim();
            // Combine date and time: "2025-06-23" + "10:30:15" = "2025-06-23T10:30:15"
            const combinedDateTime = dateStr + "T" + timeStr;
            const parsedDate = new Date(combinedDateTime);
            if (!isNaN(parsedDate.getTime())) {
              date = parsedDate.toISOString();
            }
          } catch (e) {
            // Use current date if parsing fails
          }
        } else if (dateIndex >= 0 && values[dateIndex]) {
          try {
            const dateStr = values[dateIndex].trim();
            // Handle case where date might include time
            const parsedDate = new Date(
              dateStr.replace(" ", "T") + (dateStr.includes("T") ? "" : "Z")
            );
            if (!isNaN(parsedDate.getTime())) {
              date = parsedDate.toISOString();
            }
          } catch (e) {
            // Use current date if parsing fails
          }
        }

        if (!isNaN(timeValue) && timeValue > 0) {
          // Get comment and scramble, removing quotes
          const comment =
            commentIndex >= 0 && values[commentIndex]
              ? values[commentIndex]
                  .replace(/^"|"$/g, "")
                  .replace(/""/g, '"') || null
              : null;
          const scramble =
            scrambleIndex >= 0 && values[scrambleIndex]
              ? values[scrambleIndex].replace(/^"|"$/g, "").replace(/""/g, '"')
              : "";

          importedTimes.push({
            time: timeValue,
            date: date,
            scramble: scramble,
            penalty: penalty,
            comment: comment,
          });
        }
      }
    }

    return importedTimes;
  }

  // Function to parse multi-event CSV format
  function parseMultiEventCSV(content) {
    const lines = content.split("\n").filter((line) => line.trim());

    if (lines.length < 2) {
      showGestureFeedback("Invalid CSV format");
      return [];
    }

    // Detect CSV format (comma vs semicolon separated)
    const firstLine = lines[0];
    const separator = firstLine.includes(";") ? ";" : ",";

    // Parse headers using the same method as data lines
    const headers = parseCSVLine(lines[0]).map((h) => h.toLowerCase().trim());

    // Detect format type
    if (headers.includes("no.") && separator === ";") {
      // cstimer format
      return parseCstimerCSV(content);
    }

    // Your desired format or legacy format
    const eventIdIndex = headers.findIndex((h) => h.includes("event id"));
    const customSessionIndex = headers.findIndex((h) =>
      h.includes("custom session")
    );
    const sessionNameIndex = headers.findIndex((h) =>
      h.includes("session name")
    );
    const eventIndex = headers.findIndex(
      (h) => h.includes("event") && !h.includes("event id")
    );
    const sessionIndex = headers.findIndex(
      (h) =>
        h.includes("session") &&
        !h.includes("session name") &&
        !h.includes("custom session")
    );
    const puzzleTypeIndex = headers.findIndex((h) => h.includes("puzzle type"));
    const timeSecondsIndex = headers.findIndex((h) =>
      h.includes("time (seconds)")
    );
    const penaltyIndex = headers.findIndex((h) => h.includes("penalty"));
    const scrambleIndex = headers.findIndex((h) => h.includes("scramble"));
    const dateIndex = headers.findIndex((h) => h.includes("date"));

    if (timeSecondsIndex === -1) {
      showGestureFeedback("CSV must contain 'Time (seconds)' column");
      return [];
    }

    const eventData = {};
    let totalImportedTimes = 0;
    let createdSessions = 0;

    // Process each data line
    for (let i = 1; i < lines.length; i++) {
      const values = parseCSVLine(lines[i]);

      if (values.length <= timeSecondsIndex) {
        continue; // Skip invalid lines
      }

      // Determine event/session using new format
      let targetKey = "333"; // Default event
      let isCustomSession = false;
      let sessionName = "";

      // Check if this is the new format with "Custom Session" column
      if (customSessionIndex >= 0 && values[customSessionIndex]) {
        const customSessionValue = values[customSessionIndex]
          .trim()
          .toUpperCase();
        isCustomSession = customSessionValue === "TRUE";

        if (isCustomSession) {
          // Custom session - use Session Name and Puzzle Type
          if (sessionNameIndex >= 0 && values[sessionNameIndex]) {
            sessionName = values[sessionNameIndex].trim();
          }

          // Skip if no valid session name
          if (!sessionName) {
            continue;
          }

          let puzzleType = "333"; // Default
          if (puzzleTypeIndex >= 0 && values[puzzleTypeIndex]) {
            puzzleType = values[puzzleTypeIndex].trim();
          } else if (eventIdIndex >= 0 && values[eventIdIndex]) {
            // Fallback to Event ID if no Puzzle Type
            puzzleType = values[eventIdIndex].trim();
          }

          // Create session ID from name
          const sessionId = sessionName
            .toLowerCase()
            .replace(/[^a-z0-9]/g, "_");
          targetKey = `session_${sessionId}`;

          // Check if session exists, if not create it
          let existingSession = customSessions.find((s) => s.id === sessionId);
          if (!existingSession) {
            const newSession = {
              id: sessionId,
              name: sessionName,
              puzzleType: puzzleType,
              createdAt: new Date().toISOString(),
            };
            customSessions.push(newSession);
            createdSessions++;
          }
        } else {
          // Regular event - use Event ID or Puzzle Type
          if (eventIdIndex >= 0 && values[eventIdIndex]) {
            targetKey = values[eventIdIndex].trim();
          } else if (puzzleTypeIndex >= 0 && values[puzzleTypeIndex]) {
            targetKey = values[puzzleTypeIndex].trim();
          }
        }
      } else {
        // Legacy format - use old logic
        if (
          sessionIndex >= 0 &&
          values[sessionIndex] &&
          values[sessionIndex].trim()
        ) {
          // Custom session
          sessionName = values[sessionIndex].trim();
          const eventType =
            puzzleTypeIndex >= 0 && values[puzzleTypeIndex]
              ? values[puzzleTypeIndex].trim()
              : eventIndex >= 0 && values[eventIndex]
              ? values[eventIndex].trim()
              : "333";

          // Create session ID from name
          const sessionId = sessionName
            .toLowerCase()
            .replace(/[^a-z0-9]/g, "_");
          targetKey = `session_${sessionId}`;
          isCustomSession = true;

          // Check if session exists, if not create it
          let existingSession = customSessions.find((s) => s.id === sessionId);
          if (!existingSession) {
            const newSession = {
              id: sessionId,
              name: sessionName,
              puzzleType: eventType,
              createdAt: new Date().toISOString(),
            };
            customSessions.push(newSession);
            createdSessions++;
          }
        } else {
          // Regular event
          if (eventIdIndex >= 0 && values[eventIdIndex]) {
            targetKey = values[eventIdIndex].trim();
          } else if (puzzleTypeIndex >= 0 && values[puzzleTypeIndex]) {
            targetKey = values[puzzleTypeIndex].trim();
          } else if (eventIndex >= 0 && values[eventIndex]) {
            targetKey = values[eventIndex].trim();
          }
        }
      }

      // Parse time data
      const timeStr = values[timeSecondsIndex].trim();
      let timeValue = 0;
      let penalty = null;

      if (timeStr === "DNF") {
        timeValue = Infinity;
      } else {
        timeValue = parseFloat(timeStr) * 1000; // Convert to milliseconds
      }

      // Parse penalty (handle both "+2" and "2" formats)
      if (penaltyIndex >= 0 && values[penaltyIndex]) {
        const penaltyStr = values[penaltyIndex].trim();
        if (penaltyStr === "DNF") {
          penalty = "DNF";
          timeValue = Infinity;
        } else if (penaltyStr === "+2" || penaltyStr === "2") {
          penalty = "+2";
        }
      }

      // Parse date
      let date = new Date().toISOString();
      if (dateIndex >= 0 && values[dateIndex] && values[dateIndex].trim()) {
        const parsedDate = new Date(values[dateIndex].trim());
        if (!isNaN(parsedDate.getTime())) {
          date = parsedDate.toISOString();
        }
      }

      // Parse scramble
      let scramble = "";
      if (scrambleIndex >= 0 && values[scrambleIndex]) {
        scramble = values[scrambleIndex].trim();
      }

      // Create solve object
      const solve = {
        time: timeValue,
        date: date,
        scramble: scramble,
        penalty: penalty,
      };

      // Add to event data
      if (!eventData[targetKey]) {
        eventData[targetKey] = [];
      }
      eventData[targetKey].push(solve);
      totalImportedTimes++;
    }

    // Save to localStorage and update timesMap with duplicate detection
    let totalDuplicatesSkipped = 0;
    Object.keys(eventData).forEach((key) => {
      const existingTimes = timesMap[key] || [];
      const { mergedTimes, duplicatesSkipped } = mergeTimesWithoutDuplicates(
        existingTimes,
        eventData[key]
      );

      timesMap[key] = mergedTimes;
      localStorage.setItem(`scTimer-${key}`, JSON.stringify(mergedTimes));
      totalDuplicatesSkipped += duplicatesSkipped;
    });

    // Update custom sessions in localStorage
    if (createdSessions > 0) {
      localStorage.setItem(
        "scTimer-customSessions",
        JSON.stringify(customSessions)
      );
      // Update the main event dropdown to show new sessions immediately
      updateCustomSessionsInDropdown();
    }

    // Update displays
    updateTimesList();
    updateStatsDetails();
    updateStats();
    populateEventSelector(); // Refresh to show new sessions

    // Show success message
    let message = `Imported ${
      totalImportedTimes - totalDuplicatesSkipped
    } times across ${Object.keys(eventData).length} events`;
    if (createdSessions > 0) {
      message += ` (created ${createdSessions} new sessions)`;
    }
    if (totalDuplicatesSkipped > 0) {
      message += ` (skipped ${totalDuplicatesSkipped} duplicates)`;
    }
    showGestureFeedback(message);

    return []; // Return empty array since we handled everything here
  }

  // Function to parse cstimer CSV format
  function parseCstimerCSV(content) {
    const lines = content.split("\n").filter((line) => line.trim());

    if (lines.length < 2) {
      showGestureFeedback("Invalid cstimer CSV format");
      return [];
    }

    // Parse headers (semicolon separated)
    const headers = lines[0].split(";").map((h) => h.toLowerCase().trim());
    const noIndex = headers.findIndex((h) => h.includes("no"));
    const timeIndex = headers.findIndex((h) => h.includes("time"));
    const commentIndex = headers.findIndex((h) => h.includes("comment"));
    const scrambleIndex = headers.findIndex((h) => h.includes("scramble"));
    const dateIndex = headers.findIndex((h) => h.includes("date"));

    if (timeIndex === -1) {
      showGestureFeedback("cstimer CSV must contain 'Time' column");
      return [];
    }

    const times = [];
    let totalImportedTimes = 0;

    // Process each data line
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(";");

      if (values.length <= timeIndex) {
        continue; // Skip invalid lines
      }

      // Parse time and penalty from cstimer format
      const timeStr = values[timeIndex].trim();
      let timeValue = 0;
      let penalty = null;

      if (timeStr === "DNF" || timeStr.includes("DNF")) {
        timeValue = Infinity;
        penalty = "DNF";
      } else if (timeStr.includes("(+2)")) {
        // Format: "17.010(+2)"
        const cleanTime = timeStr.replace("(+2)", "");
        timeValue = parseFloat(cleanTime) * 1000;
        penalty = "+2";
      } else {
        // Regular time
        timeValue = parseFloat(timeStr) * 1000;
      }

      // Parse date
      let date = new Date().toISOString();
      if (dateIndex >= 0 && values[dateIndex] && values[dateIndex].trim()) {
        const parsedDate = new Date(values[dateIndex].trim());
        if (!isNaN(parsedDate.getTime())) {
          date = parsedDate.toISOString();
        }
      }

      // Parse scramble
      let scramble = "";
      if (scrambleIndex >= 0 && values[scrambleIndex]) {
        scramble = values[scrambleIndex].trim();
      }

      // Parse comment
      let comment = "";
      if (commentIndex >= 0 && values[commentIndex]) {
        comment = values[commentIndex].trim();
      }

      // Create solve object
      const solve = {
        time: timeValue,
        date: date,
        scramble: scramble,
        penalty: penalty,
        comment: comment,
      };

      times.push(solve);
      totalImportedTimes++;
    }

    // Import to current event
    const currentKey = currentSessionId || currentEvent;
    const existingTimes = timesMap[currentKey] || [];
    const { mergedTimes, duplicatesSkipped } = mergeTimesWithoutDuplicates(
      existingTimes,
      times
    );

    timesMap[currentKey] = mergedTimes;
    localStorage.setItem(`scTimer-${currentKey}`, JSON.stringify(mergedTimes));

    // Update displays
    updateTimesList();
    updateStatsDetails();
    updateStats();

    // Show success message
    let message = `Imported ${
      totalImportedTimes - duplicatesSkipped
    } times to current event`;
    if (duplicatesSkipped > 0) {
      message += ` (skipped ${duplicatesSkipped} duplicates)`;
    }
    showGestureFeedback(message);

    return []; // Return empty array since we handled everything here
  }

  // Helper function to check for duplicate solves
  function isDuplicateSolve(newSolve, existingTimes) {
    return existingTimes.some((existingSolve) => {
      // Check if time, date, and scramble match exactly
      const timeMatch = Math.abs(existingSolve.time - newSolve.time) < 1; // Within 1ms
      const dateMatch = existingSolve.date === newSolve.date;
      const scrambleMatch =
        (existingSolve.scramble || "") === (newSolve.scramble || "");

      return timeMatch && dateMatch && scrambleMatch;
    });
  }

  // Helper function to merge times avoiding duplicates
  function mergeTimesWithoutDuplicates(existingTimes, newTimes) {
    const mergedTimes = [...existingTimes];
    let duplicatesSkipped = 0;

    newTimes.forEach((newSolve) => {
      if (!isDuplicateSolve(newSolve, mergedTimes)) {
        mergedTimes.push(newSolve);
      } else {
        duplicatesSkipped++;
      }
    });

    return { mergedTimes, duplicatesSkipped };
  }

  // Helper function to parse CSV line with proper quote handling
  function parseCSVLine(line) {
    const values = [];
    let current = "";
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];

      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          // Escaped quote
          current += '"';
          i++; // Skip next quote
        } else {
          // Toggle quote state
          inQuotes = !inQuotes;
        }
      } else if (char === "," && !inQuotes) {
        // End of field
        values.push(current);
        current = "";
      } else {
        current += char;
      }
    }

    // Add the last field
    values.push(current);

    return values;
  }

  // Function to handle import times
  function handleImportTimes() {
    const statsEventBtn = document.getElementById("stats-event-selector-btn");
    const selectedEvent = statsEventBtn?.getAttribute("data-event");

    // Check if this is an "all-*" view
    if (selectedEvent && selectedEvent.startsWith("all-")) {
      handleImportAllEvents(selectedEvent);
      return;
    }

    // Single event/session import
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".json,.csv,.txt";
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const content = event.target.result;
          let importedTimes = [];

          // Detect file format and parse accordingly
          const formatInfo = detectFileFormat(content, file.name);

          showGestureFeedback(
            `Detected format: ${formatInfo.format.replace("_", " ")}`
          );

          if (formatInfo.format === "scTimer_JSON") {
            const data = JSON.parse(content);
            if (data.event && data.times) {
              // Single event export
              importedTimes = data.times;
            } else if (data.events) {
              // Multi-event export - handle in separate function
              handleMultiEventImport(data);
              return;
            } else if (Array.isArray(data)) {
              importedTimes = data;
            } else if (data.times && Array.isArray(data.times)) {
              importedTimes = data.times;
            }
          } else if (formatInfo.format === "cstimer_CSV") {
            importedTimes = parseCstimerCSV(content);
          } else if (formatInfo.format === "scTimer_CSV") {
            importedTimes = parseScTimerCSV(content);
          } else {
            // Plain text format (one time per line)
            const lines = content.split("\n").filter((line) => line.trim());
            lines.forEach((line) => {
              const timeValue = parseFloat(line.trim()) * 1000; // Convert to milliseconds
              if (!isNaN(timeValue)) {
                importedTimes.push({
                  time: timeValue,
                  date: new Date().toISOString(),
                  scramble: "",
                  penalty: null,
                  comment: null,
                });
              }
            });
          }

          if (importedTimes.length > 0) {
            // Get selected event/session from stats modal
            const statsEventBtn = document.getElementById(
              "stats-event-selector-btn"
            );
            const selectedEvent = statsEventBtn?.getAttribute("data-event");
            const sessionId = statsEventBtn?.getAttribute("data-session-id");

            // Determine storage key based on stats modal selection
            let storageKey;
            if (sessionId) {
              // Custom session selected in stats modal
              storageKey = `session_${sessionId}`;
            } else if (selectedEvent) {
              // Default event selected in stats modal
              storageKey = selectedEvent;
            } else {
              // Fallback to current context
              storageKey = currentSessionId
                ? `session_${currentSessionId}`
                : currentEvent;
            }

            // Get existing times for the selected event/session
            const existingTimes = timesMap[storageKey] || [];

            // Merge with existing times and sort by date (most recent first)
            const mergedTimes = [...importedTimes, ...existingTimes];
            mergedTimes.sort((a, b) => new Date(b.date) - new Date(a.date));
            timesMap[storageKey] = mergedTimes;

            // Save to localStorage using the main saveTimes function
            saveTimes();

            // Update displays
            updateTimesList();
            updateStatsDetails();
            updateStats();

            // Refresh the all-events view if currently viewing it
            const currentViewType = selectedEvent;
            if (currentViewType && currentViewType.startsWith("all-")) {
              populateAllEventsView(currentViewType);
            }

            // Show success message
            showGestureFeedback(`Imported ${importedTimes.length} times`);
          }
        } catch (error) {
          console.error("Import error:", error);
          showGestureFeedback("Import failed");
        }
      };
      reader.readAsText(file);
    };
    input.click();
  }

  // Function to handle multi-event JSON import
  function handleMultiEventImport(data) {
    if (!data.events || typeof data.events !== "object") {
      showGestureFeedback("Invalid multi-event export format");
      return;
    }

    let totalImportedTimes = 0;
    let importedEvents = 0;
    let createdSessions = 0;
    let totalDuplicatesSkipped = 0;

    // Process each event in the export
    Object.keys(data.events).forEach((eventName) => {
      const eventData = data.events[eventName];

      if (!eventData.times || !Array.isArray(eventData.times)) {
        return; // Skip invalid event data
      }

      if (eventData.isCustomSession) {
        // Handle custom session import
        const sessionId = eventData.eventId.replace("session_", "");

        // Check if session already exists
        let existingSession = customSessions.find((s) => s.id === sessionId);

        if (!existingSession) {
          // Create new custom session
          const newSession = {
            id: sessionId,
            name: eventName,
            puzzleType: eventData.puzzleType || "333", // Default to 3x3x3 if not specified
            createdAt: new Date().toISOString(),
          };

          customSessions.push(newSession);
          createdSessions++;
        }

        // Import times to session with duplicate detection
        const storageKey = `session_${sessionId}`;
        const existingTimes = timesMap[storageKey] || [];
        const { mergedTimes, duplicatesSkipped } = mergeTimesWithoutDuplicates(
          existingTimes,
          eventData.times
        );

        timesMap[storageKey] = mergedTimes;
        localStorage.setItem(
          `scTimer-${storageKey}`,
          JSON.stringify(mergedTimes)
        );
        totalDuplicatesSkipped += duplicatesSkipped;
      } else {
        // Handle regular event import (WCA and non-WCA) with duplicate detection
        const eventId = eventData.eventId;
        const existingTimes = timesMap[eventId] || [];
        const { mergedTimes, duplicatesSkipped } = mergeTimesWithoutDuplicates(
          existingTimes,
          eventData.times
        );

        timesMap[eventId] = mergedTimes;
        localStorage.setItem(`scTimer-${eventId}`, JSON.stringify(mergedTimes));
        totalDuplicatesSkipped += duplicatesSkipped;
      }

      totalImportedTimes += eventData.times.length;
      importedEvents++;
    });

    // Update custom sessions in localStorage and dropdown
    if (createdSessions > 0) {
      localStorage.setItem(
        "scTimer-customSessions",
        JSON.stringify(customSessions)
      );
      // Update the main event dropdown to show new sessions immediately
      updateCustomSessionsInDropdown();
    }

    // Update displays
    updateTimesList();
    updateStatsDetails();
    updateStats();
    populateEventSelector(); // Refresh to show new sessions

    // Show success message
    let message = `Imported ${
      totalImportedTimes - totalDuplicatesSkipped
    } times across ${importedEvents} events`;
    if (createdSessions > 0) {
      message += ` (created ${createdSessions} new sessions)`;
    }
    if (totalDuplicatesSkipped > 0) {
      message += ` (skipped ${totalDuplicatesSkipped} duplicates)`;
    }
    showGestureFeedback(message);
  }

  // Function to handle import for all events views
  function handleImportAllEvents(viewType) {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".json,.csv,.txt";
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const content = event.target.result;

          // Detect file format first
          const formatInfo = detectFileFormat(content, file.name);

          if (formatInfo.format === "scTimer_JSON") {
            const data = JSON.parse(content);

            // Check if this is a multi-event export file
            if (data.exportType && data.events) {
              // This is a multi-event export file
              let importedEvents = 0;
              let totalImportedTimes = 0;

              Object.keys(data.events).forEach((eventName) => {
                const eventData = data.events[eventName];
                const eventId = eventData.eventId;
                const times = eventData.times;

                if (times && Array.isArray(times) && times.length > 0) {
                  // Get existing times for this event
                  const existingTimes = timesMap[eventId] || [];

                  // Merge with existing times and sort by date (most recent first)
                  const mergedTimes = [...times, ...existingTimes];
                  mergedTimes.sort(
                    (a, b) => new Date(b.date) - new Date(a.date)
                  );
                  timesMap[eventId] = mergedTimes;

                  // Save to localStorage using the main saveTimes function
                  // (timesMap is already updated above)

                  importedEvents++;
                  totalImportedTimes += times.length;
                }
              });

              if (importedEvents > 0) {
                // Save to localStorage using the main saveTimes function
                saveTimes();

                // Update displays
                updateTimesList();
                updateStatsDetails();
                updateStats();

                // Refresh the all-events view to show updated data
                const statsEventBtn = document.getElementById(
                  "stats-event-selector-btn"
                );
                const currentViewType =
                  statsEventBtn?.getAttribute("data-event");
                if (currentViewType && currentViewType.startsWith("all-")) {
                  populateAllEventsView(currentViewType);
                }

                // Show success message
                showGestureFeedback(
                  `Imported ${totalImportedTimes} times across ${importedEvents} events`
                );
              } else {
                showGestureFeedback("No valid times found in file");
              }
            } else {
              // This might be a single event file, show error for all-events import
              showGestureFeedback(
                "Please select a multi-event export file for bulk import"
              );
            }
          } else if (
            formatInfo.format === "scTimer_CSV" ||
            formatInfo.format === "cstimer_CSV"
          ) {
            // Handle CSV import for all events
            parseMultiEventCSV(content);
          } else {
            showGestureFeedback("Unsupported file format for bulk import");
          }
        } catch (error) {
          console.error("Import error:", error);
          showGestureFeedback("Import failed - invalid file format");
        }
      };
      reader.readAsText(file);
    };
    input.click();
  }

  // Function to handle export times as JSON
  function handleExportJSON() {
    const statsEventBtn = document.getElementById("stats-event-selector-btn");
    const selectedEvent = statsEventBtn?.getAttribute("data-event");

    // Check if this is an "all-*" view
    if (selectedEvent && selectedEvent.startsWith("all-")) {
      handleExportAllEvents(selectedEvent);
      return;
    }

    // Single event/session export - use selected event from stats modal
    const times = getSelectedSessionData();
    const sessionId = statsEventBtn?.getAttribute("data-session-id");
    const sessionName = statsEventBtn?.getAttribute("data-session-name");

    if (times.length === 0) {
      showGestureFeedback("No times to export");
      return;
    }

    // Determine export name based on stats modal selection
    let exportEventName;
    if (sessionId && sessionName) {
      // Custom session
      exportEventName = sessionName.replace(/[^a-zA-Z0-9]/g, "_");
    } else {
      // Default event
      exportEventName = selectedEvent || currentEvent;
    }

    // Create export data in consistent format
    const isCustomSession = sessionId && sessionName;
    const eventId = isCustomSession ? sessionId : selectedEvent || currentEvent;
    const puzzleType = isCustomSession
      ? customSessions.find((s) => s.id === sessionId.replace("session_", ""))
          ?.puzzleType || currentEvent
      : selectedEvent || currentEvent;

    const exportData = {
      exportType: "single-event",
      exportDate: new Date().toISOString(),
      events: {
        [exportEventName]: {
          eventId: eventId,
          puzzleType: puzzleType,
          event: isCustomSession
            ? getSimpleEventName(puzzleType)
            : exportEventName,
          isCustomSession: isCustomSession,
          totalSolves: times.length,
          times: times.map((solve) => ({
            time: solve.time,
            date: solve.date,
            scramble: solve.scramble || "",
            penalty: solve.penalty || null,
            result: formatTime(solve.time, solve.penalty),
            isScrambleEdited: solve.isScrambleEdited || false,
            comment: solve.comment || null,
          })),
        },
      },
      totalSolves: times.length,
      totalEvents: 1,
    };

    // Create and download JSON file
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json",
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `scTimer_${exportEventName}_${
      new Date().toISOString().split("T")[0]
    }.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // Show success message
    showGestureFeedback(`Exported ${times.length} times`);
  }

  // Function to handle export times as CSV
  function handleExportCSV() {
    const statsEventBtn = document.getElementById("stats-event-selector-btn");
    const selectedEvent = statsEventBtn?.getAttribute("data-event");

    // Check if this is an "all-*" view
    if (selectedEvent && selectedEvent.startsWith("all-")) {
      handleExportAllEventsCSV(selectedEvent);
      return;
    }

    // Single event/session export - use selected event from stats modal
    const times = getSelectedSessionData();
    const sessionId = statsEventBtn?.getAttribute("data-session-id");
    const sessionName = statsEventBtn?.getAttribute("data-session-name");

    if (times.length === 0) {
      showGestureFeedback("No times to export");
      return;
    }

    // Determine export name and event display name based on stats modal selection
    let exportEventName;
    let eventDisplayName;
    if (sessionId && sessionName) {
      // Custom session
      exportEventName = sessionName.replace(/[^a-zA-Z0-9]/g, "_");
      eventDisplayName = sessionName;
    } else {
      // Default event
      const eventId = selectedEvent || currentEvent;
      exportEventName = eventId;
      eventDisplayName = getSimpleEventName(eventId);
    }

    // Create scTimer CSV content with your updated format
    const csvHeaders = [
      "Event ID",
      "Custom Session",
      "Session Name",
      "Event",
      "Puzzle Type",
      "Solve Number",
      "Time (seconds)",
      "Penalty",
      "Comment",
      "Scramble",
      "Edited Scramble",
      "Date",
      "Time",
    ];
    let csvContent = csvHeaders.join(",") + "\n";

    times.forEach((solve, index) => {
      const solveNumber = index + 1;

      // Format time and penalty for your updated CSV format
      let timeSeconds = "";
      let penaltyStr = "";

      if (solve.penalty === "DNF") {
        timeSeconds =
          solve.time === Infinity ? "DNF" : (solve.time / 1000).toFixed(3);
        penaltyStr = "DNF";
      } else if (solve.penalty === "+2") {
        timeSeconds = (solve.time / 1000).toFixed(3);
        penaltyStr = "2"; // Your format uses "2" instead of "+2"
      } else {
        timeSeconds = (solve.time / 1000).toFixed(3);
        penaltyStr = "";
      }

      // Format date and time in your desired format
      const date = new Date(solve.date);
      const dateStr =
        String(date.getDate()).padStart(2, "0") +
        "/" +
        String(date.getMonth() + 1).padStart(2, "0") +
        "/" +
        date.getFullYear();

      const timeStr =
        String(date.getHours()).padStart(2, "0") +
        ":" +
        String(date.getMinutes()).padStart(2, "0") +
        ":" +
        String(date.getSeconds()).padStart(2, "0");

      // Escape commas and quotes in text fields for proper CSV format
      const comment = solve.comment
        ? `"${solve.comment.replace(/"/g, '""')}"`
        : "";
      const scramble = solve.scramble
        ? `"${solve.scramble.replace(/"/g, '""')}"`
        : "";

      // Determine fields for your updated format
      const isCustomSession = sessionId && sessionName;
      const eventIdStr = isCustomSession
        ? sessionId
        : selectedEvent || currentEvent;
      const customSessionStr = isCustomSession ? "TRUE" : "FALSE";
      const sessionNameStr = isCustomSession ? sessionName : eventDisplayName;
      const puzzleType = isCustomSession
        ? customSessions.find((s) => s.id === sessionId.replace("session_", ""))
            ?.puzzleType || currentEvent
        : selectedEvent || currentEvent;
      const editedScramble = solve.isScrambleEdited ? "TRUE" : "FALSE";

      const csvRow = [
        eventIdStr, // Event ID
        customSessionStr, // Custom Session (TRUE/FALSE)
        sessionNameStr, // Session Name
        `"${eventDisplayName}"`, // Event (display name)
        puzzleType, // Puzzle Type
        solveNumber, // Solve Number
        timeSeconds, // Time (seconds)
        penaltyStr, // Penalty
        comment, // Comment
        scramble, // Scramble
        editedScramble, // Edited Scramble
        dateStr, // Date
        timeStr, // Time
      ].join(",");

      csvContent += csvRow + "\n";
    });

    // Create and download CSV file with UTF-8 BOM for proper encoding
    const BOM = "\uFEFF"; // UTF-8 BOM
    const blob = new Blob([BOM + csvContent], {
      type: "text/csv;charset=utf-8;",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `scTimer_${exportEventName}_${
      new Date().toISOString().split("T")[0]
    }.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // Show success message
    showGestureFeedback(`Exported ${times.length} times as CSV`);
  }

  // Function to handle export for all events views
  function handleExportAllEvents(viewType) {
    // Define event categories
    const wcaEvents = [
      "333",
      "222",
      "444",
      "555",
      "666",
      "777",
      "333bf",
      "333fm",
      "333oh",
      "clock",
      "minx",
      "pyram",
      "skewb",
      "sq1",
      "444bf",
      "555bf",
      "333mb",
    ];
    const nonWcaEvents = [
      "fto",
      "master_tetraminx",
      "kilominx",
      "redi_cube",
      "baby_fto",
    ];

    let eventsToExport = [];
    let exportName = "";

    switch (viewType) {
      case "all-wca":
        eventsToExport = wcaEvents.filter(
          (event) => timesMap[event] && timesMap[event].length > 0
        );
        exportName = "AllWCA";
        break;
      case "all-non-wca":
        eventsToExport = nonWcaEvents.filter(
          (event) => timesMap[event] && timesMap[event].length > 0
        );
        // Add custom sessions
        customSessions.forEach((session) => {
          const storageKey = `session_${session.id}`;
          if (timesMap[storageKey] && timesMap[storageKey].length > 0) {
            eventsToExport.push({
              id: storageKey,
              name: session.name,
              puzzleType: session.puzzleType,
              isCustom: true,
            });
          }
        });
        exportName = "AllNonWCA";
        break;
      case "all-events":
        eventsToExport = [...wcaEvents, ...nonWcaEvents].filter(
          (event) => timesMap[event] && timesMap[event].length > 0
        );
        // Add custom sessions
        customSessions.forEach((session) => {
          const storageKey = `session_${session.id}`;
          if (timesMap[storageKey] && timesMap[storageKey].length > 0) {
            eventsToExport.push({
              id: storageKey,
              name: session.name,
              puzzleType: session.puzzleType,
              isCustom: true,
            });
          }
        });
        exportName = "AllEvents";
        break;
    }

    if (eventsToExport.length === 0) {
      showGestureFeedback("No times to export");
      return;
    }

    // Create comprehensive export data
    const exportData = {
      exportType: viewType,
      exportDate: new Date().toISOString(),
      events: {},
    };

    let totalSolves = 0;

    eventsToExport.forEach((eventData) => {
      const isCustomSession =
        typeof eventData === "object" && eventData.isCustom;
      const eventId = isCustomSession ? eventData.id : eventData;
      const eventName = isCustomSession
        ? eventData.name
        : getSimpleEventName(eventData);
      const times = timesMap[eventId] || [];

      if (times.length > 0) {
        const eventExportData = {
          eventId: eventId,
          isCustomSession: isCustomSession,
          totalSolves: times.length,
          times: times.map((solve) => ({
            time: solve.time,
            date: solve.date,
            scramble: solve.scramble || "",
            penalty: solve.penalty || null,
            result: formatTime(solve.time, solve.penalty),
            isScrambleEdited: solve.isScrambleEdited || false,
            comment: solve.comment || null,
          })),
        };

        // Add puzzle type and event display name
        if (isCustomSession) {
          const sessionId = eventData.id.replace("session_", "");
          const session = customSessions.find((s) => s.id === sessionId);
          if (session) {
            eventExportData.puzzleType = session.puzzleType;
            eventExportData.event = getSimpleEventName(session.puzzleType);
          }
        } else {
          eventExportData.puzzleType = eventId;
          eventExportData.event = getSimpleEventName(eventId);
        }

        exportData.events[eventName] = eventExportData;
        totalSolves += times.length;
      }
    });

    exportData.totalSolves = totalSolves;
    exportData.totalEvents = Object.keys(exportData.events).length;

    // Create and download JSON file
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json",
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `scTimer_${exportName}_${
      new Date().toISOString().split("T")[0]
    }.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // Show success message
    showGestureFeedback(
      `Exported ${totalSolves} times from ${
        Object.keys(exportData.events).length
      } events`
    );
  }

  // Function to handle CSV export for all events views
  function handleExportAllEventsCSV(viewType) {
    // Define event categories
    const wcaEvents = [
      "333",
      "222",
      "444",
      "555",
      "666",
      "777",
      "333bf",
      "333fm",
      "333oh",
      "clock",
      "minx",
      "pyram",
      "skewb",
      "sq1",
      "444bf",
      "555bf",
      "333mb",
    ];
    const nonWcaEvents = [
      "fto",
      "master_tetraminx",
      "kilominx",
      "redi_cube",
      "baby_fto",
    ];

    let eventsToExport = [];
    let exportName = "";

    switch (viewType) {
      case "all-wca":
        eventsToExport = wcaEvents.filter(
          (event) => timesMap[event] && timesMap[event].length > 0
        );
        exportName = "AllWCA";
        break;
      case "all-non-wca":
        eventsToExport = nonWcaEvents.filter(
          (event) => timesMap[event] && timesMap[event].length > 0
        );
        // Add custom sessions
        customSessions.forEach((session) => {
          const storageKey = `session_${session.id}`;
          if (timesMap[storageKey] && timesMap[storageKey].length > 0) {
            eventsToExport.push({
              id: storageKey,
              name: session.name,
              puzzleType: session.puzzleType,
              isCustom: true,
            });
          }
        });
        exportName = "AllNonWCA";
        break;
      case "all-events":
        eventsToExport = [...wcaEvents, ...nonWcaEvents].filter(
          (event) => timesMap[event] && timesMap[event].length > 0
        );
        // Add custom sessions
        customSessions.forEach((session) => {
          const storageKey = `session_${session.id}`;
          if (timesMap[storageKey] && timesMap[storageKey].length > 0) {
            eventsToExport.push({
              id: storageKey,
              name: session.name,
              puzzleType: session.puzzleType,
              isCustom: true,
            });
          }
        });
        exportName = "AllEvents";
        break;
    }

    if (eventsToExport.length === 0) {
      showGestureFeedback("No times to export");
      return;
    }

    // Create scTimer CSV content with your updated format
    const csvHeaders = [
      "Event ID",
      "Custom Session",
      "Session Name",
      "Event",
      "Puzzle Type",
      "Solve Number",
      "Time (seconds)",
      "Penalty",
      "Comment",
      "Scramble",
      "Edited Scramble",
      "Date",
      "Time",
    ];
    let csvContent = csvHeaders.join(",") + "\n";
    let totalSolves = 0;

    eventsToExport.forEach((eventData) => {
      const isCustomSession =
        typeof eventData === "object" && eventData.isCustom;
      const eventId = isCustomSession ? eventData.id : eventData;
      const eventName = isCustomSession
        ? eventData.name
        : getSimpleEventName(eventData);
      const times = timesMap[eventId] || [];

      if (times.length > 0) {
        times.forEach((solve, index) => {
          const solveNumber = index + 1;

          // Format time and penalty for your desired CSV format
          let timeSeconds = "";
          let penaltyStr = "";

          if (solve.penalty === "DNF") {
            timeSeconds =
              solve.time === Infinity ? "DNF" : (solve.time / 1000).toFixed(3);
            penaltyStr = "DNF";
          } else if (solve.penalty === "+2") {
            timeSeconds = (solve.time / 1000).toFixed(3);
            penaltyStr = "2"; // Your format uses "2" instead of "+2"
          } else {
            timeSeconds = (solve.time / 1000).toFixed(3);
            penaltyStr = "";
          }

          // Format date and time in your desired format
          const date = new Date(solve.date);
          const dateStr =
            String(date.getDate()).padStart(2, "0") +
            "/" +
            String(date.getMonth() + 1).padStart(2, "0") +
            "/" +
            date.getFullYear();

          const timeStr =
            String(date.getHours()).padStart(2, "0") +
            ":" +
            String(date.getMinutes()).padStart(2, "0") +
            ":" +
            String(date.getSeconds()).padStart(2, "0");

          // Escape commas and quotes in text fields for proper CSV format
          const comment = solve.comment
            ? `"${solve.comment.replace(/"/g, '""')}"`
            : "";
          const scramble = solve.scramble
            ? `"${solve.scramble.replace(/"/g, '""')}"`
            : "";

          // Determine fields for your updated format
          const eventIdStr = isCustomSession ? eventData.id : eventId;
          const customSessionStr = isCustomSession ? "TRUE" : "FALSE";
          const sessionNameStr = isCustomSession ? eventData.name : eventName;
          const eventDisplayName = isCustomSession
            ? getSimpleEventName(eventData.puzzleType)
            : eventName;
          const puzzleType = isCustomSession ? eventData.puzzleType : eventId;
          const editedScramble = solve.isScrambleEdited ? "TRUE" : "FALSE";

          const csvRow = [
            eventIdStr, // Event ID
            customSessionStr, // Custom Session (TRUE/FALSE)
            sessionNameStr, // Session Name
            `"${eventDisplayName}"`, // Event (display name)
            puzzleType, // Puzzle Type
            solveNumber, // Solve Number
            timeSeconds, // Time (seconds)
            penaltyStr, // Penalty
            comment, // Comment
            scramble, // Scramble
            editedScramble, // Edited Scramble
            dateStr, // Date
            timeStr, // Time
          ].join(",");

          csvContent += csvRow + "\n";
        });
        totalSolves += times.length;
      }
    });

    // Create and download CSV file with UTF-8 BOM for proper encoding
    const BOM = "\uFEFF"; // UTF-8 BOM
    const blob = new Blob([BOM + csvContent], {
      type: "text/csv;charset=utf-8;",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `scTimer_${exportName}_${
      new Date().toISOString().split("T")[0]
    }.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // Show success message
    showGestureFeedback(
      `Exported ${totalSolves} times from ${eventsToExport.length} events as CSV`
    );
  }

  // Helper function to get current storage key
  function getCurrentStorageKey() {
    const statsEventBtn = document.getElementById("stats-event-selector-btn");
    if (statsEventBtn) {
      const sessionId = statsEventBtn.getAttribute("data-session-id");
      if (sessionId) {
        return `session_${sessionId}`;
      }
    }
    return currentSessionId ? `session_${currentSessionId}` : currentEvent;
  }

  // Initialize import/export functionality
  function initImportExportButtons() {
    const importBtn = document.getElementById("import-times-btn");
    const exportJSONBtn = document.getElementById("export-json-btn");
    const exportCSVBtn = document.getElementById("export-csv-btn");

    if (importBtn) {
      importBtn.addEventListener("click", handleImportTimes);
    }
    if (exportJSONBtn) {
      exportJSONBtn.addEventListener("click", handleExportJSON);
    }
    if (exportCSVBtn) {
      exportCSVBtn.addEventListener("click", handleExportCSV);
    }
  }
});
