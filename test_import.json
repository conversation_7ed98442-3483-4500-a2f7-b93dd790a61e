{"exportType": "multi-event", "exportDate": "2024-12-25T10:00:00.000Z", "version": "1.0", "events": {"3×3×3": {"eventId": "333", "isCustomSession": false, "times": [{"time": 15234, "date": "2024-12-20T10:00:00.000Z", "scramble": "R U R' U' R' F R2 U' R' U' R U R' F'", "penalty": null}, {"time": 14567, "date": "2024-12-20T10:05:00.000Z", "scramble": "F R U' R' U' R U R' F' R U R' U' R' F R F'", "penalty": null}, {"time": 16789, "date": "2024-12-20T10:10:00.000Z", "scramble": "R U2 R' U' R U' R' F R U R' U' R' F R", "penalty": "+2"}, {"time": 13456, "date": "2024-12-20T10:15:00.000Z", "scramble": "F R U R' U' F' U R U' R' U' R U R' U' R' F R F'", "penalty": null}, {"time": Infinity, "date": "2024-12-20T10:20:00.000Z", "scramble": "R U R' U R U2 R' F R U R' U' R' F R", "penalty": "DNF"}, {"time": 15890, "date": "2024-12-20T10:25:00.000Z", "scramble": "F R U' R' U R U R' F' R U R' U' R' F R F'", "penalty": null}, {"time": 14234, "date": "2024-12-20T10:30:00.000Z", "scramble": "R U R' U' R' F R2 U' R' U' R U R' F'", "penalty": null}, {"time": 16123, "date": "2024-12-20T10:35:00.000Z", "scramble": "F R U R' U' F' R U R' U' R' F R F'", "penalty": null}, {"time": 15567, "date": "2024-12-20T10:40:00.000Z", "scramble": "R U2 R' U' R U' R' F R U R' U' R' F R", "penalty": null}, {"time": 14890, "date": "2024-12-20T10:45:00.000Z", "scramble": "F R U' R' U' R U R' F' R U R' U' R' F R F'", "penalty": null}]}, "4×4×4": {"eventId": "444", "isCustomSession": false, "times": [{"time": 65234, "date": "2024-12-21T09:00:00.000Z", "scramble": "Rw U2 x Rw Uw2 Rw D2 Rw' U2 Rw' D2 Rw2 x'", "penalty": null}, {"time": 72456, "date": "2024-12-21T09:10:00.000Z", "scramble": "Uw2 Fw2 F Rw2 R2 B2 Rw2 Fw2 Uw2 F2 Rw2", "penalty": null}, {"time": 68789, "date": "2024-12-21T09:20:00.000Z", "scramble": "F2 Rw2 B2 U2 Rw2 F2 Uw2 Rw2 B2 Uw2", "penalty": "+2"}, {"time": 59876, "date": "2024-12-21T09:30:00.000Z", "scramble": "Rw2 Uw2 Rw2 F2 Uw2 B2 Rw2 F2 Uw2 Rw2", "penalty": null}, {"time": 71234, "date": "2024-12-21T09:40:00.000Z", "scramble": "B2 Uw2 F2 Rw2 Uw2 B2 F2 Uw2 Rw2 B2", "penalty": null}]}, "Pyraminx": {"eventId": "pyram", "isCustomSession": false, "times": [{"time": 8567, "date": "2024-12-22T14:00:00.000Z", "scramble": "L R U R' U' L' U L U' L'", "penalty": null}, {"time": 7234, "date": "2024-12-22T14:05:00.000Z", "scramble": "R U R' U R U2 R' L U L' U L U2 L'", "penalty": null}, {"time": 9456, "date": "2024-12-22T14:10:00.000Z", "scramble": "L U L' U' L U L' R U R' U' R U R'", "penalty": null}, {"time": 6789, "date": "2024-12-22T14:15:00.000Z", "scramble": "R U2 R' U R U R' L U2 L' U L U L'", "penalty": null}, {"time": 8123, "date": "2024-12-22T14:20:00.000Z", "scramble": "L R U R' U' L' U L U' L' R U R' U' R U R'", "penalty": null}]}, "Master Tetraminx": {"eventId": "master_tetraminx", "isCustomSession": false, "times": [{"time": 125678, "date": "2024-12-23T16:00:00.000Z", "scramble": "R++ D-- R++ D++ R-- D++ R++ D-- R-- D++ R++ D--", "penalty": null}, {"time": 134567, "date": "2024-12-23T16:15:00.000Z", "scramble": "R-- D++ R++ D-- R++ D++ R-- D-- R++ D++ R-- D++", "penalty": null}, {"time": 142345, "date": "2024-12-23T16:30:00.000Z", "scramble": "R++ D++ R-- D-- R++ D-- R-- D++ R++ D-- R-- D++", "penalty": "+2"}, {"time": 118234, "date": "2024-12-23T16:45:00.000Z", "scramble": "R-- D-- R++ D++ R-- D++ R++ D-- R-- D++ R++ D--", "penalty": null}]}, "Speed Practice": {"eventId": "session_speed_practice", "isCustomSession": true, "puzzleType": "333", "times": [{"time": 12345, "date": "2024-12-24T08:00:00.000Z", "scramble": "R U R' U' R' F R2 U' R' U' R U R' F'", "penalty": null}, {"time": 11567, "date": "2024-12-24T08:05:00.000Z", "scramble": "F R U R' U' F' U R U' R' U' R U R' U' R' F R F'", "penalty": null}, {"time": 13789, "date": "2024-12-24T08:10:00.000Z", "scramble": "R U2 R' U' R U' R' F R U R' U' R' F R", "penalty": null}, {"time": 10456, "date": "2024-12-24T08:15:00.000Z", "scramble": "F R U' R' U R U R' F' R U R' U' R' F R F'", "penalty": null}, {"time": 12890, "date": "2024-12-24T08:20:00.000Z", "scramble": "R U R' U R U2 R' F R U R' U' R' F R", "penalty": null}, {"time": 11234, "date": "2024-12-24T08:25:00.000Z", "scramble": "F R U' R' U' R U R' F' R U R' U' R' F R F'", "penalty": null}]}, "Mirror Blocks Practice": {"eventId": "session_mirror_blocks_practice", "isCustomSession": true, "puzzleType": "333", "times": [{"time": 45678, "date": "2024-12-24T12:00:00.000Z", "scramble": "R U R' U' R' F R2 U' R' U' R U R' F'", "penalty": null}, {"time": 52345, "date": "2024-12-24T12:10:00.000Z", "scramble": "F R U R' U' F' R U R' U' R' F R F'", "penalty": "+2"}, {"time": 48567, "date": "2024-12-24T12:20:00.000Z", "scramble": "R U2 R' U' R U' R' F R U R' U' R' F R", "penalty": null}, {"time": 41234, "date": "2024-12-24T12:30:00.000Z", "scramble": "F R U' R' U R U R' F' R U R' U' R' F R F'", "penalty": null}]}, "Big Cubes Session": {"eventId": "session_big_cubes_session", "isCustomSession": true, "puzzleType": "555", "times": [{"time": 145678, "date": "2024-12-24T15:00:00.000Z", "scramble": "Rw Uw2 Rw Dw2 Rw' Uw2 Rw' Dw2 Rw2 Uw2 Rw2 Dw2", "penalty": null}, {"time": 152345, "date": "2024-12-24T15:20:00.000Z", "scramble": "Dw2 Fw2 Rw2 Uw2 Bw2 Rw2 Fw2 Dw2 Bw2 Uw2", "penalty": null}, {"time": 138567, "date": "2024-12-24T15:40:00.000Z", "scramble": "Rw2 Dw2 Fw2 Uw2 Rw2 Bw2 Dw2 Fw2 Uw2 Bw2", "penalty": "+2"}]}}}